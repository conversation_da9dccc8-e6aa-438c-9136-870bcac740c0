import { Card, Radio } from "antd";
import { Braces, LayoutPanelTop, List, Rocket } from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import TabList from "../../common/TabList";
import BasicSettingTab from "./BasicSettingTab";
import BrandingContentTab from "./BrandingContentTab";
import DynamicVariablesTab from "./DynamicVariablesTab";
import DeploymentTab from "./DeploymentTab";
import useHttp from "../../../hooks/use-http";
import { apiGenerator, imageObj } from "../../../util/functions";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";

const createDefaultPage = (type = "Single Column") => ({
  sitemapLabel: "",
  navigationType: type,
  sections: [],
  columnCount: 0,
  // type.includes("Multi Column") ? 2 : undefined,
});

const WebsiteTabList = React.memo(
  ({
    websiteData,
    templateData,
    formData,
    setFormData,
    loading,
    setLoading,
    onCancel,
    handleSubmit,
    globalvariables,
  }) => {
    const api = useHttp();
    const [previewMode, setPreviewMode] = useState("basicSetting");
    const [reset, setReset] = useState(false);
    const [dynamicForms, setdynamicForms] = useState({});
    // const [globalvariables, setGlobalvariables] = useState(null);
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    // console.log(selectedTemplate, "selectedTemplate");
    // Branding state
    const [pageList, setPageList] = useState([]);
    const [selectPage, setSelectPage] = useState(null);
    // console.log(
    //   dynamicForms,
    //   "dynamicForms",
    //   formData,
    //   selectPage,
    //   globalvariables,
    //   websiteData,
    //   selectedTemplate,
    //   templateData
    // );
    useEffect(() => {
      setReset((pr) => !pr);
    }, [previewMode]);

    const templateGetByIdHander = useCallback(
      async (versionId, extraObj = {}) => {
        try {
          api.sendRequest(
            apiGenerator(CONSTANTS.API.templateVersions.getById, {
              id: versionId,
            }),
            (res) => {
              console.log("Template fetched for editing:", res);
              setSelectedTemplate((pr) => ({
                ...pr,
                ...extraObj,
                templateVersionData: res?.data,
              }));

              const mediaObj = res?.data?.mediaSet?.media;
              setFormData((prevForm) => {
                let dForm = {};
                res?.data?.pageData?.forEach((page) => {
                  if (page?.type == "dynamic") {
                    if (prevForm?.websiteDynamicPageData[page?.name]) {
                      dForm[page?.name] =
                        prevForm.websiteDynamicPageData[page?.name];
                      setdynamicForms((prev) => ({
                        ...prev,
                        [page?.name]:
                          prevForm?.websiteDynamicPageData[page?.name],
                      }));
                    } else {
                      dForm[page?.name] = createDefaultPage();
                      setdynamicForms((prev) => ({
                        ...prev,
                        [page?.name]: createDefaultPage(),
                      }));
                    }
                  }
                });

                return {
                  ...prevForm,
                  websiteDynamicPageData: dForm,
                  templateContent: res?.data?.content?.content
                    ? JSON.parse(JSON.stringify(res?.data?.content?.content))
                    : {},
                  templateMediaSet: {
                    ...(mediaObj ? imageObj(mediaObj || {}, true) : {}),
                  },

                  pageData: res?.data?.pageData,
                  templateVersiondata: res?.data,
                };
              });
            }
          );
        } catch (error) {
          console.error("Error fetching template:", error);
        }
      },
      []
    );

    const tabContents = {
      basicSetting: {
        key: "basicSetting",
        label: "Basic Settings",
        icon: <List className="tw-w-4 tw-h-4 tw-mr-2" />,
        content: (
          <BasicSettingTab
            formData={formData}
            website={websiteData}
            setFormData={setFormData}
            templateData={templateData}
            onCancel={onCancel}
            handleSubmit={handleSubmit}
            setdynamicForms={setdynamicForms}
            dynamicForms={dynamicForms}
            templateGetByIdHander={templateGetByIdHander}
            selectedTemplate={selectedTemplate}
            setSelectedTemplate={setSelectedTemplate}
            loading={loading}

            // saving={saving}
          />
        ),
      },
      brandingPreview: {
        key: "brandingPreview",
        label: "Branding",
        // label: "Branding & Content Preview",
        icon: <LayoutPanelTop className="tw-w-4 tw-h-4 tw-mr-2" />,
        content: (
          <BrandingContentTab
            // pagesData={pages}
            // components={components}
            formData={formData}
            setFormData={setFormData}
            templateData={formData?.templateVersiondata || []}
            onCancel={onCancel}
            handleSubmit={handleSubmit}
            reset={reset}
            setReset={setReset}
            pageList={pageList}
            setPageList={setPageList}
            selectPage={selectPage}
            setSelectPage={setSelectPage}
            selectedTemplate={selectedTemplate}
            loading={loading}

            // templateData={templateData}
          />
        ),
      },
      dynamicVariable: {
        key: "dynamicVariable",
        label: "Dynamic Variables",
        icon: <Braces className="tw-w-4 tw-h-4 tw-mr-2" />,
        content: (
          <DynamicVariablesTab
            formData={formData}
            setFormData={setFormData}
            onCancel={onCancel}
            handleSubmit={handleSubmit}
            loading={loading}
            // saving={saving}
            templateData={templateData}
            globalvariables={globalvariables}
          />
        ),
      },
      deployment: {
        key: "deployment",
        label: "Deployment",
        icon: <Rocket className="tw-w-4 tw-h-4 tw-mr-2" />,
        content: (
          <DeploymentTab
            website={websiteData}
            formData={formData}
            setFormData={setFormData}
            templateData={templateData}
            loading={loading}
            onCancel={onCancel}
            handleSubmit={handleSubmit}
          />
        ),
      },
    };

    return (
      <div className="tw-h-full tw-flex tw-flex-col ">
        {/* Preview/Code Toggle */}
        <TabList
          tabContents={tabContents}
          setPreviewMode={setPreviewMode}
          previewMode={previewMode}
        />
      </div>
    );
  }
);

export default WebsiteTabList;
