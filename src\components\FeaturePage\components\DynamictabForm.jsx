import {
  <PERSON><PERSON>,
  Card,
  Col,
  Form,
  Input,
  Row,
  Select,
  Tooltip,
  Modal,
  Divider,
  Space,
  Tabs,
  Typography,
  Switch,
  InputNumber,
} from "antd";
import {
  MinusCircle,
  Plus,
  HelpCircle,
  ExternalLink,
  MoreHorizontalIcon,
} from "lucide-react";
import React, { useEffect, useMemo, memo, useState } from "react";
import { serviceAreaDKey } from "../../../util/content";
import ExtraModalBody from "./ExtraModalBody";
const { Text } = Typography;

// Memoized form item components for better performance
const FormItemWithTooltip = memo(({ label, tooltip, children, ...props }) => (
  <Form.Item
    label={
      tooltip ? (
        <Space className="tw-items-center">
          <Text strong>{label}</Text>{" "}
          <Tooltip title={tooltip}>
            <HelpCircle className="tw-w-4 tw-h-4 tw-inline tw-mb-1" />
          </Tooltip>
        </Space>
      ) : (
        label
      )
    }
    {...props}
  >
    {children}
  </Form.Item>
));

// Extra editor modal for editing "extra" key/values (and repeated arrays)
const ExtraEditorModal = memo(
  ({
    // subField,
    open,
    onCancel,
    sectionIndex,
    itemIndex,
    onSave,
    currentPage,
    tab,
  }) => {
    const [form] = Form.useForm();
    const parentForm = Form.useFormInstance?.();
    const isServiceAreaD = tab == serviceAreaDKey;
    // Default fields based on form type
    const getDefaultFields = () => {
      if (isServiceAreaD) {
        return [
          { key: "breadcrumbName", value: "" },
          { key: "sitemapLabel", value: "" },
          { key: "state", value: "" },
        ];
      }
      return [
        { key: "breadcrumbName", value: "" },
        { key: "sitemapLabel", value: "" },
      ];
    };

    // Initialize form with existing data or defaults
    React.useEffect(() => {
      if (!open) return;
      const existingExtra =
        parentForm?.getFieldValue([
          "sections",
          sectionIndex,
          "sectionItems",
          itemIndex,
          "extra",
        ]) || {};
      const simpleFields = [];
      const arrayGroups = [];

      // Process existing extra data
      Object.keys(existingExtra).forEach((key) => {
        if (Array.isArray(existingExtra[key])) {
          // This is a repeated item
          arrayGroups.push({
            arrayKey: key,
            items: existingExtra[key].map((item) => ({
              key: Object.keys(item)[0] || "key",
              value: Object.values(item)[0] || "",
            })),
          });
        } else {
          // This is a single item
          simpleFields.push({ key, value: existingExtra[key] });
        }
      });

      // Add default fields if not present
      const defaultFields = getDefaultFields();
      defaultFields.forEach((defaultField) => {
        if (!simpleFields.find((f) => f.key === defaultField.key)) {
          simpleFields.unshift(defaultField);
        }
      });

      form.setFieldsValue({
        simpleFields,
        arrayGroups,
      });
    }, [open, isServiceAreaD]);

    const handleOk = () => {
      form.validateFields().then((values) => {
        // console.log(values, "values");
        const extraObj = {};

        // Process simple fields
        if (values.simpleFields) {
          values.simpleFields.forEach((field) => {
            if (field.key && field.value !== undefined) {
              extraObj[field.key] = field.value;
            }
          });
        }

        // Process array groups
        if (values.arrayGroups) {
          values.arrayGroups.forEach((group) => {
            if (group.arrayKey && group.items) {
              extraObj[group.arrayKey] = group.items.map((item) => ({
                [item.key]: item.value,
              }));
            }
          });
        }

        onSave(extraObj);
      });
    };

    // const tabItems = [
    //   {
    //     key: "simple",
    //     label: "Single Items",
    //     children: (
    //       <Form form={form}>
    //         <Form.List name="simpleFields">
    //           {(fields, { add, remove }) => (
    //             <div className="tw-space-y-4">
    //               <div className="tw-space-y-3">
    //                 {fields.map((field, index) => {
    //                   const isDefaultField = isServiceAreaD
    //                     ? field.name <= 2 // breadcrumbName, sitemapLabel, state
    //                     : field.name <= 1; // breadcrumbName, sitemapLabel

    //                   return (
    //                     <Row key={field.key} gutter={[8, 8]} align="middle">
    //                       <Col span={8}>
    //                         <Form.Item
    //                           {...field}
    //                           name={[field.name, "key"]}
    //                           className="tw-mb-0"
    //                           rules={[
    //                             { required: true, message: "Key is required" },
    //                           ]}
    //                         >
    //                           <Input
    //                             disabled={isDefaultField}
    //                             placeholder="Field key"
    //                           />
    //                         </Form.Item>
    //                       </Col>
    //                       <Col span={14}>
    //                         <Form.Item
    //                           {...field}
    //                           className="tw-mb-0"
    //                           name={[field.name, "value"]}
    //                           rules={[
    //                             {
    //                               required: true,
    //                               message: "Value is required",
    //                             },
    //                           ]}
    //                         >
    //                           <Input placeholder="Field value" />
    //                         </Form.Item>
    //                       </Col>
    //                       <Col span={2}>
    //                         {!isDefaultField && (
    //                           <Button
    //                             danger
    //                             size="small"
    //                             onClick={() => remove(field.name)}
    //                             icon={<MinusCircle className="tw-w-4 tw-h-4" />}
    //                           />
    //                         )}
    //                       </Col>
    //                     </Row>
    //                   );
    //                 })}
    //               </div>
    //               <Button
    //                 type="dashed"
    //                 onClick={() => add({ key: "", value: "" })}
    //                 block
    //                 icon={<Plus className="tw-w-4 tw-h-4" />}
    //               >
    //                 Add Section Item
    //               </Button>
    //             </div>
    //           )}
    //         </Form.List>
    //       </Form>
    //     ),
    //   },
    //   {
    //     key: "arrays",
    //     label: "Repeated Items",
    //     children: (
    //       <Form form={form}>
    //         <Form.List name="arrayGroups">
    //           {(groups, { add: addGroup, remove: removeGroup }) => (
    //             <div className="tw-space-y-4">
    //               <div className="tw-space-y-4">
    //                 {groups?.map((groupField, groupIndex) => (
    //                   <Card
    //                     key={groupField?.key}
    //                     size="small"
    //                     className="tw-bg-blue-50"
    //                   >
    //                     <Space direction="vertical" className="tw-w-full">
    //                       <Row gutter={[8, 8]} align="middle">
    //                         <Col span={23}>
    //                           <Form.Item
    //                             {...groupField}
    //                             className="tw-mb-0"
    //                             name={[groupField.name, "arrayKey"]}
    //                             rules={[
    //                               {
    //                                 required: true,
    //                                 message: "Array key is required",
    //                               },
    //                             ]}
    //                           >
    //                             <Input placeholder="Key" />
    //                           </Form.Item>
    //                         </Col>
    //                         <Col span={1}>
    //                           <Button
    //                             danger
    //                             size="small"
    //                             onClick={() => removeGroup(groupField.name)}
    //                             icon={<MinusCircle className="tw-w-4 tw-h-4" />}
    //                           ></Button>
    //                         </Col>
    //                       </Row>

    //                       <Divider className="!tw-my-0" />

    //                       <div className="tw-space-y-2">
    //                         <div className="tw-flex tw-items-center tw-justify-between">
    //                           <span className="tw-text-sm tw-font-medium">
    //                             Items
    //                           </span>
    //                         </div>

    //                         <Form.List name={[groupField.name, "items"]}>
    //                           {(
    //                             items,
    //                             { add: addItem, remove: removeItem }
    //                           ) => (
    //                             <>
    //                               {items.map((itemField, itemIndex) => (
    //                                 <Form.Item key={itemField.key} shouldUpdate>
    //                                   {() => {
    //                                     return (
    //                                       <Row gutter={[8, 8]} align="middle">
    //                                         <Col span={11}>
    //                                           <Form.Item
    //                                             {...itemField}
    //                                             className="tw-mb-0"
    //                                             name={[itemField.name, "key"]}
    //                                             rules={[
    //                                               {
    //                                                 required: true,
    //                                                 message: `Key is required`,
    //                                               },
    //                                             ]}
    //                                           >
    //                                             <Input
    //                                               placeholder={`Enter key`}
    //                                             />
    //                                           </Form.Item>
    //                                         </Col>
    //                                         <Col span={11}>
    //                                           <Form.Item
    //                                             {...itemField}
    //                                             name={[itemField.name, "value"]}
    //                                             className="tw-mb-0"
    //                                             rules={[
    //                                               {
    //                                                 required: true,
    //                                                 message: `Value is required`,
    //                                               },
    //                                             ]}
    //                                           >
    //                                             <Input
    //                                               placeholder={`Enter value`}
    //                                             />
    //                                           </Form.Item>
    //                                         </Col>
    //                                         <Col span={2}>
    //                                           <Button
    //                                             danger
    //                                             className=""
    //                                             size="small"
    //                                             onClick={() =>
    //                                               removeItem(itemField.name)
    //                                             }
    //                                             icon={
    //                                               <MinusCircle className="tw-w-4 tw-h-4" />
    //                                             }
    //                                           />
    //                                         </Col>
    //                                       </Row>
    //                                     );
    //                                   }}
    //                                 </Form.Item>
    //                               ))}
    //                               <Button
    //                                 type="dashed"
    //                                 onClick={() =>
    //                                   addItem({ key: "", value: "" })
    //                                 }
    //                                 block
    //                                 icon={<Plus className="tw-w-4 tw-h-4" />}
    //                               >
    //                                 Add Item
    //                               </Button>
    //                             </>
    //                           )}
    //                         </Form.List>
    //                       </div>
    //                     </Space>
    //                   </Card>
    //                 ))}
    //               </div>

    //               <Button
    //                 type="dashed"
    //                 onClick={() =>
    //                   addGroup({
    //                     arrayKey: "",
    //                     items: [{ key: "", value: "" }],
    //                   })
    //                 }
    //                 block
    //                 icon={<Plus className="tw-w-4 tw-h-4" />}
    //               >
    //                 Add Repeated Items
    //               </Button>
    //             </div>
    //           )}
    //         </Form.List>
    //       </Form>
    //     ),
    //   },
    // ];

    return (
      <Modal
        open={open}
        onCancel={onCancel}
        onOk={handleOk}
        title="Extra"
        width={900}
        okText="Save Changes"
        cancelText="Cancel"
      >
        <ExtraModalBody form={form} isServiceAreaD={isServiceAreaD} />
        {/* <Tabs defaultActiveKey="simple" items={tabItems} className="tw-mt-4" /> */}
      </Modal>
    );
  }
);

// Button that opens ExtraEditorModal and persists changes back to the form
const ExtraFieldButton = memo(
  ({ tab, form, subField, sectionIndex, itemIndex, currentPage }) => {
    const [open, setOpen] = useState(false);
    // const initialExtra =
    //   form?.getFieldValue([
    //     "sections",
    //     sectionIndex,
    //     "sectionItems",
    //     itemIndex,
    //     "extra",
    //   ]) ||
    //   form?.getFieldValue(["sections", sectionIndex, "sectionItems", itemIndex])
    //     ?.extra;
    const handleSave = (extraObj) => {
      // console.log(extraObj, "extraObj");
      // Set the value back into the form using the same nested path
      form?.setFields([
        {
          name: ["sections", sectionIndex, "sectionItems", itemIndex, "extra"],
          value: extraObj,
        },
      ]);
      setOpen(false);
    };

    return (
      <>
        <Button
          icon={<Plus className="tw-mt-1" />}
          onClick={() => setOpen(true)}
        ></Button>
        <ExtraEditorModal
          // subField={subField}
          open={open}
          onCancel={() => setOpen(false)}
          onSave={handleSave}
          sectionIndex={sectionIndex}
          itemIndex={itemIndex}
          currentPage={currentPage}
          tab={tab}
        />
      </>
    );
  }
);
const SectionItem = memo(({ field, remove, currentPage, tab }) => (
  <Card
    key={field.key}
    size="small"
    title={`Section ${field.name + 1}`}
    extra={
      <MinusCircle
        className="tw-w-5 tw-h-5 tw-text-gray-500 tw-cursor-pointer"
        onClick={() => remove(field.name)}
      />
    }
  >
    <Form.Item
      {...field}
      label={<Text strong>{"Section Label"}</Text>}
      name={[field.name, "sectionLabel"]}
      // rules={[{ required: true, message: "Section label is required" }]}
    >
      <Input placeholder="Enter section label" />
    </Form.Item>
    <SectionItemsList tab={tab} field={field} currentPage={currentPage} />
  </Card>
));

const SectionItemsList = memo(({ field, currentPage, tab }) => {
  const form = Form.useFormInstance?.() || null;
  return (
    <Form.List name={[field?.name, "sectionItems"]}>
      {(fields, { add, remove }) => (
        <div className="">
          <h3 className="tw-font-semibold tw-text-lg tw-mb-2">Section Items</h3>
          {console.log(fields)}
          {fields?.map((subField) => (
            <Row key={subField?.key} gutter={[16, 0]}>
              <Col span={9}>
                <Form.Item
                  {...subField}
                  label={<Text strong>{"Label"}</Text>}
                  name={[subField.name, "label"]}
                  rules={[{ required: true, message: "Label is required" }]}
                >
                  <Input placeholder="Enter label" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  {...subField}
                  label={<Text strong>{"Slug"}</Text>}
                  name={[subField.name, "slug"]}
                  rules={[
                    { required: true, message: "Slug is required" },
                    {
                      pattern: /^[a-z0-9-]+$/,
                      message: "Only lowercase letters, numbers, and hyphens",
                    },
                  ]}
                >
                  <Input placeholder="Enter slug" />
                </Form.Item>
              </Col>
              <Col span={3}>
                <Form.Item
                  {...subField}
                  label={<Text strong>{"Custom"}</Text>}
                  name={[subField?.name, "isCustom"]}
                  valuePropName="checked" // ✅ tells Form to use checked instead of value
                  rules={
                    [
                      // { required: true, message: "Custom is required" },
                      // {
                      //   pattern: /^[a-z0-9-]+$/,
                      //   message: "Only lowercase letters, numbers, and hyphens",
                      // },
                    ]
                  }
                >
                  {/* <Input placeholder="Enter slug" /> */}
                  <Switch />
                </Form.Item>
              </Col>

              <Col span={2}>
                <Form.Item
                  {...subField}
                  label={<Text strong>{"Extra"}</Text>}
                  name={[subField.name, "extra"]}
                  valuePropName="__extra_dummy"
                  getValueFromEvent={() => undefined}
                >
                  <ExtraFieldButton
                    form={form}
                    subField={subField}
                    sectionIndex={field.name}
                    itemIndex={subField.name}
                    currentPage={currentPage}
                    tab={tab}
                  />
                </Form.Item>
              </Col>
              <Col span={1} className="tw-mt-2 tw-flex tw-items-center">
                <MinusCircle
                  width={20}
                  height={20}
                  className="tw-text-gray-500 tw-w-5 tw-h-5 tw-cursor-pointer"
                  onClick={() => remove(subField.name)}
                />
              </Col>
            </Row>
          ))}
          <Button
            type="dashed"
            onClick={() => add({ label: "", slug: "" })}
            block
            icon={<Plus className="tw-w-4 tw-h-4" />}
          >
            Add Section Item
          </Button>
        </div>
      )}
    </Form.List>
  );
});

const DynamictabForm = memo(
  ({ tab, currentPage, onValuesChange, form, onFormInstanceCreated }) => {
    // Create form instance if not provided
    const [internalForm] = Form.useForm();
    const formInstance = form || internalForm;
    // Notify parent about form instance creation
    useEffect(() => {
      if (onFormInstanceCreated && !form) {
        // console.log("Form instance created for tab:", tab);
        onFormInstanceCreated(tab, formInstance);
      }
    }, [tab, formInstance, onFormInstanceCreated, form]);
    // Memoize the validation rules
    const validationRules = useMemo(
      () => ({
        sitemapLabel: [
          // { required: true, message: "Sitemap label is required" },
          { max: 100, message: "Sitemap label cannot exceed 100 characters" },
        ],
        navigationType: [
          // { required: true, message: "Navigation type is required" },
        ],
        columnCount: [
          // { required: true, message: "Column count is required" },
          {
            type: "number",
            min: 1,
            max: 6,
            message: "Column count must be between 1 and 6",
          },
        ],
      }),
      []
    );

    // Memoize navigation options
    const navigationOptions = useMemo(
      () => [
        { value: "Single Column", label: "Single Column" },
        { value: "Multi Column", label: "Multi Column" },
      ],
      []
    );

    // Efficient form initialization
    // useEffect(() => {
    //   if (formInstance && currentPage) {
    //     const formValues = {
    //       sitemapLabel: currentPage.sitemapLabel || "",
    //       navigationType: currentPage.navigationType || "Single Column",
    //       columnCount: currentPage.columnCount || 0,
    //       sections: Array.isArray(currentPage.sections)
    //         ? currentPage.sections.map((section) => ({
    //             sectionLabel: section.sectionLabel || "",
    //             sectionItems: Array.isArray(section.sectionItems)
    //               ? section.sectionItems.map((item) => ({
    //                   label: item.label || "",
    //                   slug: item.slug ? item.slug.toLowerCase() : "",
    //                 }))
    //               : [],
    //           }))
    //         : [],
    //     };

    //     // Force form to re-render with new values
    //     formInstance.resetFields();
    //     formInstance.setFieldsValue(formValues);

    //     console.log(
    //       "Form initialized for tab:",
    //       tab,
    //       "with values:",
    //       formValues
    //     );
    //   }
    // }, [tab, currentPage, formInstance]);

    // Function to process extra data for JSON format
    const processExtraData = (extraData) => {
      if (!extraData) return {};

      const processed = {};
      Object.keys(extraData).forEach((key) => {
        if (Array.isArray(extraData[key])) {
          // Convert array format to key-value pairs
          processed[key] = extraData[key].map((item) => {
            if (typeof item === "object" && item !== null) {
              return item;
            }
            return { [key]: item };
          });
        } else {
          processed[key] = extraData[key];
        }
      });
      return processed;
    };

    // Function to import JSON data
    const importJSONData = (jsonData) => {
      if (jsonData && jsonData.sections) {
        const processedSections = jsonData.sections.map((section) => ({
          ...section,
          sectionItems: section.sectionItems?.map((item) => ({
            ...item,
            extra: processExtraData(item.extra),
          })),
        }));

        formInstance.setFieldsValue({
          ...jsonData,
          sections: processedSections,
        });
      }
    };

    // Debounced form change handler
    const handleFormChange = useMemo(() => {
      let timeoutId;
      return (changedValues, allValues) => {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          // Process form values before sending to parent
          const processedValues = {
            ...allValues,
            sections: allValues.sections?.map((section) => ({
              ...section,
              sectionItems: section.sectionItems?.map((item) => ({
                ...item,
                slug: item.slug?.toLowerCase(),
                extra: processExtraData(item.extra),
              })),
            })),
          };
          onValuesChange(changedValues, processedValues);
        }, 300);
      };
    }, [onValuesChange]);

    // Initialize form with current page data
    useEffect(() => {
      if (currentPage && formInstance) {
        importJSONData(currentPage);
      }
    }, [currentPage, formInstance]);

    return (
      <Form
        form={formInstance}
        name={`dynamic_form_${tab}`}
        layout="vertical"
        size="large"
        onValuesChange={handleFormChange}
        initialValues={currentPage}
        className=""
      >
        <Row gutter={[24, 24]}>
          <Col xs={12} lg={8}>
            <FormItemWithTooltip
              label="Sitemap Label"
              name="sitemapLabel"
              tooltip="The label that will appear in the sitemap"
              rules={validationRules.sitemapLabel}
              className="tw-mb-0"
            >
              <Input placeholder="Enter sitemap label" />
            </FormItemWithTooltip>
          </Col>
          <Col xs={12} lg={8}>
            <FormItemWithTooltip
              label="Navigation Type"
              name="navigationType"
              tooltip="Select how the page content will be laid out"
              rules={validationRules.navigationType}
              className="tw-mb-0"
            >
              <Select
                placeholder="Select navigation type"
                options={navigationOptions}
              />
            </FormItemWithTooltip>
          </Col>
          <Col xs={24} lg={8}>
            <FormItemWithTooltip
              label="Breadcrumb Name"
              name="breadcrumbName"
              tooltip="The name that will appear in the breadcrumb"
              className="tw-mb-0"
              // rules={validationRules.}
            >
              <Input placeholder="Enter breadcrumb name" />
            </FormItemWithTooltip>
          </Col>
          <Col xs={24} lg={8}>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.navigationType !== currentValues.navigationType
              }
            >
              {({ getFieldValue }) =>
                getFieldValue("navigationType")?.includes("Multi Column") && (
                  <FormItemWithTooltip
                    label="Column Count"
                    name="columnCount"
                    tooltip="Number of columns in the grid layout"
                    rules={validationRules?.columnCount}
                  >
                    <InputNumber
                      controls={false}
                      style={{ width: "100%" }}
                      placeholder="Enter column count"
                    />
                  </FormItemWithTooltip>
                )
              }
            </Form.Item>
          </Col>
        </Row>

        <Form.List name="sections">
          {(fields, { add, remove }) => (
            <div className="tw-space-y-4">
              {fields?.map((field) => (
                <SectionItem
                  key={field?.key}
                  field={field}
                  remove={remove}
                  form={formInstance}
                  currentPage={currentPage}
                  tab={tab}
                />
              ))}
              <Button
                type="dashed"
                onClick={() => add({ sectionLabel: "", sectionItems: [] })}
                block
                icon={<Plus className="tw-w-4 tw-h-4" />}
              >
                Add Section
              </Button>
            </div>
          )}
        </Form.List>
      </Form>
    );
  }
);

FormItemWithTooltip.displayName = "FormItemWithTooltip";
SectionItem.displayName = "SectionItem";
SectionItemsList.displayName = "SectionItemsList";
DynamictabForm.displayName = "DynamictabForm";

export default DynamictabForm;
