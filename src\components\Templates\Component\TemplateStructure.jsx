import { Input, <PERSON>lt<PERSON>, <PERSON>, Switch, <PERSON><PERSON>, <PERSON>, Col, Tag } from "antd";
import {
  GripVertical,
  Trash2,
  Search,
  ChevronDown,
  ChevronUp,
  Loader2,
  X,
  Info,
  Settings,
  Plus,
} from "lucide-react";
import React, { useRef, useState, useEffect } from "react";
import { useDrag, useDragLayer, useDrop } from "react-dnd";
import { DND_TYPES } from "../../../util/content";
import SearchBar from "../../common/SearchBar";
import PageSettingsModal from "./PageSettingsModal";
import useDebounce from "../../../hooks/useDebounce";
import runtimeCache from "../../../utils/runtimeCache";
import Paragraph from "antd/es/typography/Paragraph";
import { useParams } from "react-router-dom";
import useHttp from "../../../hooks/use-http";
import { apiGenerator } from "../../../util/functions";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import VersionSelect from "./VersionSelectField";

// const ITEM_TYPE = "STRUCTURE_ITEM";

const TemplateStructure = ({
  isTemplateStructureOpen,
  setIsTemplateStructureOpen,
  formData,
  setFormData,
  removePageFromTemplate,
  moveComponent,
  onComponentFieldChange = () => {}, // Global onChange handler
  screenSize,
  templateId = "current", // Current template ID for caching
  onPageRemoved, // Callback when page is removed from template
  onPageAdded, // Callback when page is added to template
  setReloadTrigger,
  reloadTrigger,
  setSelectPage,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const api = useHttp();
  // Page Settings Modal state
  const { isDraggingGlobal } = useDragLayer((monitor) => ({
    isDraggingGlobal: monitor.isDragging(),
  }));
  const { id } = useParams();
  // Update local dragging state based on global drag state
  useEffect(() => {
    if (isDraggingGlobal) {
      setIsDragging(true);
    } else {
      // Small delay to allow drop to complete
      const timer = setTimeout(() => setIsDragging(false), 100);
      return () => clearTimeout(timer);
    }
  }, [isDraggingGlobal]);
  // console.log(formData, "formData in structure");
  // Debouncing effect for search
  useEffect(() => {
    if (searchTerm) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
        setIsSearching(false);
      }, 300); // 300ms debounce delay

      return () => {
        clearTimeout(timer);
      };
    } else {
      setDebouncedSearchTerm("");
      setIsSearching(false);
    }
  }, [searchTerm]);

  // Global onChange handler for all component fields
  // const handleGlobalFieldChange = (index, field, value, extraData = {}) => {
  //   if (onComponentFieldChange) {
  //     onComponentFieldChange(index, field, value, extraData);
  //   }
  // };

  // Draggable and droppable structure item component
  const StructureItem = ({
    index = 0,
    componentName,
    onRemove,
    pageVersionId,
  }) => {
    const componentData = formData?.pageData?.find(
      (p) => p?.pageVersionId == pageVersionId
    );
    const [open, setOpen] = useState(false);
    // console.log(componentData, "componentData", pageVersionId);
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [slugValue, setSlugValue] = useState(
      componentData?.versionData?.urlSlug || ""
    );
    const [navIndexValue, setNavIndexValue] = useState(
      componentData?.navbarIndex || 0
    );
    const debouncedNavIndex = useDebounce(navIndexValue, 400);
    const [isPageSettingsModalVisible, setIsPageSettingsModalVisible] =
      useState(null);
    const debouncedSlug = useDebounce(slugValue, 400);
    const versionsData = componentData?.pageversions?.find(
      (v) => v.id == componentData?.version
    );
    useEffect(() => {
      if (
        // debouncedSlug != "" &&
        debouncedSlug !== componentData?.versionData?.urlSlug
      ) {
        handleSavePageSettings({
          versionData: {
            ...componentData?.versionData,
            urlSlug: debouncedSlug,
          },
        });
      }
      if (
        // debouncedNavIndex != "" &&
        debouncedNavIndex !== componentData?.navbarIndex
      ) {
        handleSavePageSettings({ navbarIndex: +debouncedNavIndex });
      }
    }, [debouncedSlug, debouncedNavIndex]);
    const handleSavePageSettings = (updatedData) => {
      // console.log("Saved page settings:", updatedData);
      // navbarIndex, type, showNavbar
      setFormData((pr) => {
        const updatedPages = pr?.pageData?.map((p, idx) =>
          p?.id == componentData?.id
            ? {
                ...p,
                ...updatedData?.versionData,
                ...updatedData,
                // versionData: { ...p?.versionData, ...updatedData },
                // urlSlug: updatedData?.urlSlug,
              }
            : p
        );
        return { ...pr, pageData: updatedPages };
      });
      // handleGlobalFieldChange(index, "settings", updatedData);
      setIsPageSettingsModalVisible(null);
      // setSelectedPageForSettings(null);
    };

    const handleSlugChange = (e) => {
      setSlugValue(e.target.value);
    };

    return (
      <div
        className={`tw-bg-white tw-border tw-border-[#D9DBDF] tw-rounded-2xl tw-p-4 tw-transition-all tw-duration-200 ${"tw-hover:tw-bg-gray-200"}`}
      >
        {/* Header with position and delete button */}
        <div className="tw-flex tw-flex-nowrap tw-items-center tw-justify-between tw-gap-1">
          {/* Left side */}
          <div className="tw-flex tw-items-center tw-gap-3 tw-min-w-0">
            <Paragraph
              ellipsis={{ rows: 1, expandable: false, tooltip: true }}
              className="tw-text-gray-500 tw-text-sm !tw-mb-0 tw-truncate"
            >
              {componentData?.name}
            </Paragraph>
          </div>

          {/* Right side */}
          <div className="tw-flex tw-items-center tw-gap-0">
            <Button
              type="text"
              icon={<Settings className="tw-w-4 tw-h-4 tw-text-gray-400" />}
              onClick={(e) => {
                e.stopPropagation();
                setIsPageSettingsModalVisible(componentData);
              }}
              className="tw-text-gray-400 tw-hover:tw-text-gray-500 tw-transition-colors tw-rounded-lg tw-hover:tw-bg-white"
            />
            <Button
              type="text"
              danger
              icon={<Trash2 className="tw-w-4 tw-h-4" />}
              onClick={(e) => {
                e.stopPropagation();
                runtimeCache.updateTemplatePageAvailability(
                  componentData?.id,
                  true,
                  templateId
                );
                setFormData((pr) => {
                  const updatedPages = pr?.pageData?.filter(
                    (p) => p?.id !== componentData?.id
                  );
                  return { ...pr, pageData: updatedPages };
                });
              }}
              className="tw-text-gray-400 tw-hover:tw-text-red-500 tw-transition-colors tw-rounded-lg tw-hover:tw-bg-white"
            />
            <Button
              type="text"
              icon={
                isCollapsed ? (
                  <ChevronDown className="tw-w-4 tw-h-4 tw-text-gray-400" />
                ) : (
                  <ChevronUp className="tw-w-4 tw-h-4 tw-text-gray-400" />
                )
              }
              onClick={(e) => {
                e.stopPropagation();
                setIsCollapsed(!isCollapsed);
              }}
              className="tw-bg-transparent tw-border-0 tw-cursor-pointer"
            />
          </div>
        </div>

        {/* Collapsible Content */}
        {!isCollapsed && (
          <div className="tw-mt-2 tw-space-y-2">
            {/* Version Dropdown */}
            <div className="">
              <VersionSelect
                componentData={componentData}
                handleSavePageSettings={handleSavePageSettings}
                setFormData={setFormData}
                setSelectPage={setSelectPage}
              />
            </div>

            {/* Component Name Field */}
            <div className="">
              <Input
                size="large"
                placeholder=""
                value={slugValue}
                onChange={(e) => handleSlugChange(e)}
                // handleGlobalFieldChange(index, "urlSlug", e.target.value)
                // }
                onMouseDown={(e) => e.stopPropagation()}
                onFocus={(e) => e.stopPropagation()}
                className="tw-rounded-xl  tw-text-gray-900"
              />
            </div>

            {/* Display Type Dropdown */}
            <div>
              <Select
                size="large"
                placeholder="Static"
                value={componentData?.type || "Static"}
                onChange={(value) => {
                  handleSavePageSettings({ type: value });
                  // handleGlobalFieldChange(index, "type", value)
                }}
                onMouseDown={(e) => e.stopPropagation()}
                onFocus={(e) => e.stopPropagation()}
                className="tw-w-full"
                style={{
                  borderRadius: "16px",
                }}
                options={[
                  { value: "static", label: "Static Page" },
                  { value: "dynamic", label: "Dynamic Page" },
                ]}
              />
            </div>

            <div className="tw-flex tw-items-center tw-justify-between ">
              <div>
                <span className="tw-text-center tw-text-sm tw-flex tw-items-center tw-gap-1">
                  {" "}
                  Navigation Bar{" "}
                  <Tooltip title="Position of navigation menu.">
                    <Info className="tw-w-4 tw-h-4 tw-text-gray-400" />
                  </Tooltip>
                </span>
                <Switch
                  checked={componentData?.showNavbar}
                  size="small"
                  onChange={(checked) => {
                    handleSavePageSettings({ showNavbar: checked });
                    // handleGlobalFieldChange(index, "showNavbar", checked, {
                    //   navbarIndex: 0,
                    // })
                  }}
                />
              </div>
              <div>
                <Input
                  size="large"
                  placeholder="0"
                  disabled={!componentData.showNavbar}
                  value={navIndexValue || ""}
                  onChange={(e) => {
                    setNavIndexValue(e.target.value);
                    // handleSavePageSettings({ navbarIndex: e.target.value });
                    // handleCssChange(index, e.target.value);
                    // handleGlobalFieldChange(
                    //   index,
                    //   "navbarIndex",
                    //   e.target.value
                    // );
                  }}
                  onMouseDown={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  className="tw-rounded-xl  tw-text-gray-900 tw-w-12"
                />
              </div>
            </div>
          </div>
        )}
        <PageSettingsModal
          visible={isPageSettingsModalVisible}
          onCancel={() => setIsPageSettingsModalVisible(null)}
          onSave={handleSavePageSettings}
          pageData={{
            ...isPageSettingsModalVisible?.versionData,
            name: isPageSettingsModalVisible?.name,
          }}
          // loading={pageSettingsLoading}
        />
      </div>
    );
  };
  // Enhanced remove page function with cache updates
  const handleRemovePageFromTemplate = (index) => {
    const pageToRemove = formData?.pageData?.[index];

    if (pageToRemove) {
      // console.log("Removing page from template:", pageToRemove?.name);

      // Update cache to reflect page is now available
      runtimeCache.updateTemplatePageAvailability(
        pageToRemove.id,
        true,
        templateId
      );

      setFormData((pr) => {
        const updatedPages = pr?.pageData?.filter(
          (p, idx) => p?.id !== pageToRemove?.id
        );
        return { ...pr, pageData: updatedPages };
      });

      // Notify parent component about page removal
      // if (onPageRemoved) {
      //   onPageRemoved(pageToRemove);
      // }
    }

    // Call the original remove function
    // removePageFromTemplate(index);
  };

  // Drop zone for accepting pages from TemplateLibrary
  const [{ isOver: isDropZoneOver, canDrop }, dropZone] = useDrop(
    () => ({
      accept: "PAGE_ITEM",
      drop: (item) => {
        // console.log("Page dropped in TemplateStructure:", item?.page);

        // Add page to template using functional update to avoid stale closures
        setFormData((prevData) => {
          const order = prevData?.pageData?.length || 0;
          const newPage = {
            ...item.page,
            order,
            pageId: item?.page?.id,
            pageVersionId: item?.page?.versionData?.id,
            version: item?.page?.versionData?.version,
            type: item?.page?.type || "static",
            showNavbar: false,
            navbarIndex: 0,
            url:
              item?.page?.versionData?.urlSlug ||
              `/${item?.page?.name?.toLowerCase()?.replace(/\s+/g, "-")}`,
          };

          const nextPages = [...(prevData?.pageData || []), newPage];
          // console.log(nextPages, "updatedPages", prevData?.pageData);
          return {
            ...prevData,
            pageData: nextPages,
          };
        });

        // Update cache to reflect page is no longer available
        runtimeCache.updateTemplatePageAvailability(
          item.page.id,
          false,
          templateId
        );
        // setReloadTrigger((prev) => !prev);

        // if (onPageAdded) onPageAdded(item.page);
      },
      collect: (monitor) => ({
        isOver: monitor.isOver({ shallow: true }),
        canDrop: monitor.canDrop(),
      }),
      hover: (_, monitor) => {
        // Set dragging state when hovering
        if (monitor.canDrop()) {
          setIsDragging(true);
        }
      },
    }),
    [setIsDragging] // Dependency on components to ensure fresh state
  );
  const showDropIndicator = isDropZoneOver && canDrop;
  // console.log(showDropIndicator);
  return (
    <>
      {/* SearchBar */}
      {isTemplateStructureOpen && (
        <div className="tw-p-3 md:tw-p-4 tw-bg-white">
          <SearchBar
            type="page"
            placeholder="Search components..."
            handleSearch={(e) => setSearchTerm(e)}
          />
        </div>
      )}

      <div
        ref={dropZone}
        className={`tw-bg-white tw-border-gray-200 tw-flex tw-flex-col tw-relative tw-transition-all tw-duration-300 tw-ease-in-out ${
          isTemplateStructureOpen
            ? "tw-w-full tw-h-full"
            : "tw-w-0 tw-overflow-hidden"
        }`}
      >
        {/* List */}
        <div className="tw-flex-1 tw-overflow-y-auto tw-p-3 md:tw-p-4 !tw-pt-0">
          {formData?.pageData?.length > 0 ? (
            <div className="tw-space-y-2">
              <Row gutter={[16, 16]}>
                {formData?.pageData
                  ?.map((pageComp, index) => ({
                    pageComp,
                    order: pageComp?.order || index,
                    index,
                    component: pageComp?.versionData,
                  }))
                  ?.filter(({ pageComp }) =>
                    debouncedSearchTerm
                      ? pageComp?.name
                          ?.toLowerCase()
                          ?.includes(debouncedSearchTerm.toLowerCase())
                      : true
                  )
                  ?.map(({ pageComp, order }) => (
                    <Col
                      span={24}
                      lg={12}
                      key={pageComp?.uniqueId || `col-${pageComp?.id}-${order}`}
                    >
                      <StructureItem
                        key={
                          pageComp?.uniqueId ||
                          `fallback-${pageComp?.id}-${order}`
                        }
                        pageVersionId={pageComp?.pageVersionId}
                        // idx={pageComp?.id}
                        index={order}
                        componentName={pageComp?.name}
                        onRemove={handleRemovePageFromTemplate}
                      />
                    </Col>
                  ))}
              </Row>
            </div>
          ) : (
            <div className="tw-text-center tw-py-8">
              <p className="tw-text-gray-500">No Pages added</p>
              <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                Drag pages from the library to add them here
              </p>
            </div>
          )}
        </div>

        {/* 🔹 Full overlay drop indicator */}
        {showDropIndicator && (
          <div className="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center tw-bg-blue-50/60 tw-border-2 tw-mx-3 tw-border-dashed tw-rounded-lg tw-border-blue-400 tw-z-50">
            <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
              <div className="tw-flex tw-items-center tw-space-x-3">
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
                <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
                  Drop to add page
                </p>
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce tw-animation-delay-100"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default TemplateStructure;
