import React, { useState, useEffect, useCallback } from "react";
import { Button, Input, message, Popconfirm, Tooltip } from "antd";
import { Globe, Plus, Search, Trash2 } from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { useDebounce } from "../../hooks/useDebounce";
import ScrollPagination from "../common/ScrollPagination";
import WebsiteCard from "./components/WebsiteCard";
import DuplicateWebsiteModal from "./components/DuplicateWebsiteModal";
import SearchBar from "../common/SearchBar";
import CommonButton from "../common/CommonButton";
import { useNavigate, Outlet, useLocation } from "react-router-dom";
import { getSortParams } from "../../util/content";

const WebsiteManager = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [websites, setWebsites] = useState([]);
  const [totalWebsites, setTotalWebsites] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [operationLoading, setOperationLoading] = useState({});
  const [updatedWebsite, setUpdatedWebsite] = useState(null);

  const [isDuplicateModalVisible, setIsDuplicateModalVisible] = useState(false);
  const [selectedWebsite, setSelectedWebsite] = useState(null);
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [previewWebsite, setPreviewWebsite] = useState(null);

  const api = useHttp();
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const loadWebsite = useCallback(
    async ({ page, pageSize }) => {
      try {
        // const sortParams = getSortParams(filterOption);
        const payload = {
          page,
          limit: pageSize,
          // sort: sortParams?.sort,
          // sortBy: sortParams?.sortBy,
          search: debouncedSearchTerm || "",
        };

        return new Promise((resolve, reject) => {
          api.sendRequest(
            CONSTANTS.API.websites.get,
            (res) => {
              // Handle different response structures
              const items = res?.data?.rows || [];
              const totalCount = res?.data?.count;

              resolve({
                items,
                totalCount,
              });
            },
            payload,
            null,
            (error) => {
              message.error(
                error || "Failed to load websites. Please try again."
              );
              reject(error);
            }
          );
        });
      } catch (error) {
        message.error("An unexpected error occurred while loading websites.");
        throw error;
      }
    },
    [api, debouncedSearchTerm]
  );
  const fetchWebsites = (page = 1, limit = 10, search = "") => {
    const params = { page, limit, search };
    api.sendRequest(
      CONSTANTS.API.websites.get,
      (res) => {
        setWebsites(
          page == 1 ? res?.data?.rows : [...websites, ...res?.data?.rows]
        );
        setTotalWebsites(res?.data?.count);
      },
      params,
      null,
      (error) => {
        console.error("Failed to fetch websites:", error);
        message.error(error || "Failed to fetch websites");
      }
    );
  };

  useEffect(() => {
    if (location.pathname === "/websites") {
      fetchWebsites(1, 10, debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, location.pathname]);

  const handleSearchChange = (e) => {
    setSearchTerm(e);
  };

  const handleDuplicate = (website) => {
    setSelectedWebsite(website);
    setIsDuplicateModalVisible(true);
  };

  const handleDelete = async (website) => {
    try {
      setOperationLoading((prev) => ({
        ...prev,
        [`delete-${website.id}`]: true,
      }));

      api.sendRequest(
        apiGenerator(CONSTANTS.API.websites.delete, { id: website.id }),
        (res) => {
          setUpdatedWebsite({ ...website, type: "delete" });
          message.success("Website deleted successfully!");
        },
        null,
        null,
        (error) => {
          message.error(error || "Failed to delete website. Please try again.");
        }
      );
    } catch (error) {
      message.error(
        error || "An unexpected error occurred while deleting website."
      );
    } finally {
      setOperationLoading((prev) => ({
        ...prev,
        [`delete-${website.id}`]: false,
      }));
    }
  };

  const handleEdit = (website) => {
    setUpdatedWebsite(null);
    navigate(`/websites/${website.id}`);
  };

  const handlePreview = (website) => {
    setPreviewWebsite(website);
    setIsPreviewVisible(true);
  };

  const addHandler = () => {
    setUpdatedWebsite(null);
    navigate("/websites/add");
  };

  // Empty state component
  const emptyScreen = (
    <div className="tw-text-center tw-py-12">
      <Globe className="tw-w-16 tw-h-16 tw-text-gray-400 tw-mx-auto tw-mb-4" />
      <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
        {searchTerm ? "No websites found" : "No websites yet"}
      </h3>
      <p className="tw-text-gray-500 tw-mb-4">
        {searchTerm
          ? "Try adjusting your search criteria"
          : "Create your first website from a template"}
      </p>
    </div>
  );

  if (location.pathname !== "/websites") {
    return <Outlet />;
  }
  return (
    <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
      <div className="tw-flex tw-justify-between tw-items-center tw-mb-6">
        <h1 className="tw-text-2xl tw-font-semibold">
          Websites ({totalWebsites})
        </h1>
        <CommonButton
          onClick={addHandler}
          icon={<Plus className="tw-w-4 tw-h-4 tw-mr-2" />}
          text="Add Website"
        />
      </div>
      <div className="tw-mb-6">
        <SearchBar handleSearch={handleSearchChange} type="page" />
      </div>
      <div>
        <ScrollPagination
          key={`ws-${debouncedSearchTerm}`}
          // data={websites}
          loadPage={loadWebsite}
          total={totalWebsites}
          fetchData={fetchWebsites}
          emptyScreen={emptyScreen}
          updatedItem={updatedWebsite}
          pageSize={12}
          className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-2 xl:tw-grid-cols-2 tw-gap-6"
          emptyDescription="No websites found"
          renderItem={(website) => (
            <WebsiteCard
              website={website}
              onDuplicate={handleDuplicate}
              onDelete={handleDelete}
              onEdit={handleEdit}
              onPreview={handlePreview}
              operationLoading={operationLoading}
              isLoading={api?.isLoading}
            />
          )}
          columns={{ sm: 1, md: 2, lg: 3, xl: 4 }}
        />
      </div>
      {isDuplicateModalVisible && (
        <DuplicateWebsiteModal
          visible={isDuplicateModalVisible}
          onCancel={() => setIsDuplicateModalVisible(false)}
          website={selectedWebsite}
          onSuccess={(newWebsite) => {
            setUpdatedWebsite({ ...newWebsite, type: "create" });
            setSelectedWebsite(null);
            setIsDuplicateModalVisible(false);
          }}
        />
      )}
      {previewWebsite && (
        <Image
          width={200}
          style={{ display: "none" }}
          src={previewWebsite.image}
          preview={{
            visible: isPreviewVisible,
            onVisibleChange: (vis) => setIsPreviewVisible(vis),
            afterClose: () => setPreviewWebsite(null),
          }}
        />
      )}
    </div>
  );
};

export default WebsiteManager;
