import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  Card,
  Tag,
  Avatar,
  Button,
  Space,
  Typography,
  Tooltip,
  Empty,
  Spin,
} from "antd";
import {
  Activity,
  Eye,
  Plus,
  Edit,
  Trash2,
  Copy,
  Download,
  Rocket,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import SearchBar from "../common/SearchBar";
import ActivityLogModal from "./ActivityLogModal";
import useDebounce from "../../hooks/useDebounce";
import Activitytable from "./Activitytable";

const { Title, Text } = Typography;

const ActivityLogs = () => {
  const [activityLogs, setActivityLogs] = useState([]);
  // const [loading, setLoading] = useState(false);

  return (
    <div className="tw-p-6 tw-space-y-4">
      {/* Header */}
      <div className="tw-mb-0">
        <div className="tw-flex tw-items-center tw-gap-3 tw-mb-0">
          <Activity className="tw-w-6 tw-h-6 tw-text-blue-600" />
          <Title level={2} className="tw-mb-0">
            Activity Logs ({activityLogs?.count})
          </Title>
        </div>
      </div>

      {/* Table */}
      <Activitytable
        activityLogs={activityLogs}
        setActivityLogs={setActivityLogs}
        // loading={loading}
      />
    </div>
  );
};

export default ActivityLogs;
