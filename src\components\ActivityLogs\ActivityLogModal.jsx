import React from "react";
import { Modal, Typography, Card, Space, Tag } from "antd";
import { User, Calendar, FileText, MessageSquare } from "lucide-react";

const { Title, Text, Paragraph } = Typography;

const ActivityLogModal = ({ visible, onClose, activityLog }) => {
  if (!activityLog) return null;
  return (
    <Modal
      title="Activity Log Message"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      centered
      className="activity-log-modal"
    >
      <div className="tw-space-y-4">
        {/* User Information */}
        <Card size="small" className="tw-bg-gray-50">
          <Space align="center" className="tw-w-full">
            <div className="tw-w-10 tw-h-10 tw-rounded-full tw-bg-blue-500 tw-flex tw-items-center tw-justify-center tw-text-white tw-font-semibold">
              {activityLog.user?.name?.charAt(0)?.toUpperCase() || "U"}
            </div>
            <div>
              <Text strong className="tw-text-lg">
                {activityLog.user?.name || "Unknown User"}
              </Text>
              <br />
              <Text type="secondary" className="tw-text-sm">
                {activityLog.user?.email || "No email"}
              </Text>
            </div>
          </Space>
        </Card>

        <Paragraph className="tw-mb-0 tw-mx-2 tw-whitespace-pre-wrap">
          {activityLog.message || "No message available"}
        </Paragraph>
      </div>
    </Modal>
  );
};

export default ActivityLogModal;
