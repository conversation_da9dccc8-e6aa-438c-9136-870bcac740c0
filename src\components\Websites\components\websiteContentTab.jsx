import { <PERSON><PERSON>, <PERSON>, message, Select, Typography } from "antd";
import { Upload as UploadIcon, ListCollapse, Image, Eye } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import TabList from "../../common/TabList";

import DeviceButtons from "../../common/DeviceButtons";
import {
  apiGenerator,
  autoDetectPlaceholders,
  autoDetectRepeatPlaceHolder,
  imageObj,
  replaceTemplateContent,
} from "../../../util/functions";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import runtimeCache from "../../../utils/runtimeCache";
import MediaBar from "../../Templates/Component/MediaBar";
import SinglePagePreview from "../../Templates/Component/SinglePagePreview";
import ClickBasedContentLoader from "../../Templates/Component/ClickBasedContentLoader";
import CommonButton from "../../common/CommonButton";
const { Text } = Typography;

const templatePageGenerator = ({
  formData,
  selectPage,
  dynamicpages = {},
  allVariables,
}) => {
  const pagesList = [];
  let selectedPage = {};
  const imgList = {
    ...(formData?.templateMediaSet ? formData?.templateMediaSet : {}),
    ...(formData?.FileList || formData?.mediaSet || {}),
  };

  const sPage = formData?.pageData?.find(
    (pd) => pd?.pageId == selectPage?.pageId
  );
  selectedPage = {
    ...sPage,
    slugObj: selectPage?.slugObj,
    url: selectPage?.url,
    id: selectPage?.pageId,
    name: selectPage?.oldName || selectPage?.name,
    // oldname: selectPage?.oldName,
  };

  // const selectedPage = formData?.pageData?.find(
  //   (pd) => pd?.id == selectPage?.id
  // );
  // console.log(selectedPage, "selectedPage", selectPage, formData, dynamicpages);
  if (!selectedPage) return [];
  // formData?.pageData?.map((tmpPage) => {
  const pageObj = JSON.parse(JSON.stringify(selectedPage));
  console.log(pageObj, "pageObj");
  if (pageObj?.type == "dynamic" && !pageObj?.slugObj?.isCustom) {
    // const pageName = slugObj?.label;
    pagesList?.push({
      ...replaceTemplateContent({
        page: pageObj,
        // tempContent: formData?.websiteContent,
        // content: formData?.templateContent,
        tempContent: formData?.templateContent,
        content: formData?.websiteContent,
        extraContent: pageObj?.slugObj?.extra || {},
        templateObj: formData,
        slug: pageObj?.slugObj,
        mediaFiles: imgList,
        // mediaFiles: formData?.FileList,
        allVariables,
      }),
      name: pageObj?.slugObj?.label,
      url: pageObj?.slugObj?.slug,
      //  oldName: pageObj?.name,
      // url: pageObj?.slugObj?.slug,
      // name: pageObj?.name,
    });
    allVariables[pageObj?.slugObj?.label || pageObj.newName] = {
      ...pageObj?.variable,
    };
    // console.log("calling..", pageObj);
    // if (Object.keys(dynamicpages)?.length) {
    //   const page = dynamicpages[pageObj?.name];
    //   const sections = page?.sections;
    //   if (sections?.length) {
    //     sections?.map((el) => {
    //       const slugArr = el?.sectionItems;
    //       slugArr?.map((slugObj) => {
    //         const dynamicPageObj = JSON.parse(JSON.stringify(pageObj));
    //         console.log(slugObj, "slugObj", dynamicPageObj);
    //         const pageName = slugObj?.label;
    //         pagesList?.push({
    //           ...replaceTemplateContent({
    //             page: dynamicPageObj,
    //             tempContent: formData?.content,
    //             // content: formData?.content,
    //             slug: slugObj,
    //             mediaFiles: formData?.mediaSet,
    //             // mediaFiles: formData?.FileList,
    //             allVariables,
    //           }),
    //           url: `${dynamicPageObj?.url}/${slugObj?.slug}`,
    //           name: slugObj?.label,
    //         });
    //         allVariables[pageName] = {
    //           ...dynamicPageObj.variable,
    //         };
    //       });
    //     });
    //   }
    // } else {
    //   pagesList?.push({
    //     ...replaceTemplateContent({
    //       page: pageObj,
    //       tempContent: formData?.content,
    //       // content: formData?.content,
    //       mediaFiles: formData?.mediaSet,
    //       // mediaFiles: formData?.FileList,
    //       allVariables,
    //     }),
    //     url: pageObj?.url,
    //     name: pageObj?.name,
    //   });
    //   allVariables[pageObj.name] = { ...pageObj?.variable };
    //   console.log("calling..", pageObj);
    // }
  } else {
    replaceTemplateContent({
      page: pageObj,
      tempContent: formData?.templateContent,
      content: formData?.websiteContent,
      mediaFiles: imgList,
      allVariables: allVariables,
      templateObj: formData,
    });
    allVariables[pageObj.name] = { ...pageObj?.variable };
    pagesList.push(pageObj);
  }
  // });
  return pagesList;
};

export const pageOptionGenerator = ({ formData, dynamicpages = {} }) => {
  const pagesList = [];
  console.log(dynamicpages, "dynamicpages", formData?.pageData);
  formData?.pageData?.map((pd) => {
    const pageObj = JSON.parse(JSON.stringify(pd));
    if (pageObj?.type == "dynamic") {
      if (Object.keys(dynamicpages)?.length) {
        const page = dynamicpages[pageObj?.name];
        const sections = page?.sections;
        if (sections?.length) {
          sections?.map((el) => {
            const slugArr = el?.sectionItems;
            slugArr?.map((slugObj) => {
              const dynamicPageObj = JSON.parse(JSON.stringify(pageObj));
              const pageName = slugObj?.label;
              pagesList?.push(
                {
                  ...dynamicPageObj,
                  // oldName: pageObj?.name,
                  oldName: pageObj?.name,
                  name: pageName,
                  slugObj: slugObj,
                  url: `${dynamicPageObj?.url}/${slugObj?.slug}`,
                }
                //   {
                //   ...replaceTemplateContent({
                //     page: dynamicPageObj,
                //     tempContent: formData?.content,
                //     // content: formData?.content,
                //     slug: slugObj,
                //     mediaFiles: formData?.mediaSet,
                //     // mediaFiles: formData?.FileList,
                //     allVariables,
                //   }),
                //   url: `${dynamicPageObj?.url}/${slugObj?.slug}`,
                //   name: slugObj?.label,
                // }
              );
              // allVariables[pageName] = {
              //   ...dynamicPageObj.variable,
              // };
            });
          });
        }
      } else {
        pagesList?.push(
          pageObj
          //   {
          //   ...replaceTemplateContent({
          //     page: pageObj,
          //     tempContent: formData?.content,
          //     // content: formData?.content,
          //     mediaFiles: formData?.mediaSet,
          //     // mediaFiles: formData?.FileList,
          //     allVariables,
          //   }),
          //   url: pageObj?.url,
          //   name: pageObj?.name,

          //
          // }
        );
        // allVariables[pageObj.name] = { ...pageObj?.variable };
        console.log("calling..", pageObj);
      }
    } else {
      // replaceTemplateContent({
      //   page: pageObj,
      //   tempContent: formData?.content,
      //   // content: formData?.content,
      //   mediaFiles: formData?.FileList || formData?.mediaSet || [],
      //   allVariables: allVariables,
      // });
      // allVariables[pageObj.name] = { ...pageObj?.variable };
      pagesList.push(pageObj);
    }
  });
  return pagesList;
};

const WebsiteContentTab = ({
  formData,
  setFormData,
  onCancel,
  handleSubmit,
  loading,
  pageList,
  setPageList,
  template,
  reset,
  setReset,
  selectPage,
  setSelectPage,
  dynamicpages,
  isWebsite = false,
  templateData,
  selectedTemplate,
}) => {
  const api = useHttp();
  // const [contentJSON, setContentJSON] = useState(
  //   formData?.contentJSON
  //     ? JSON.parse(JSON.stringify(formData?.contentJSON)) || {}
  //     : {}
  // );
  const [websiteJSON, setWebsiteJSON] = useState(
    formData?.websiteJSON
      ? JSON.parse(JSON.stringify(formData?.websiteJSON)) || {}
      : {}
  );
  const [fileList, setFileList] = useState(
    formData?.mediaSet ?? formData?.FileList ?? {}
  );
  const [newMediaFiles, setNewMediaFiles] = useState(
    formData?.newMediaFiles || []
  );
  const [removeMediaIds, setRemoveMediaIds] = useState(
    formData?.removeMediaIds || []
  );
  console.log(
    formData,
    "formData in content tab",
    pageList,
    websiteJSON,
    fileList
  );
  const [previewMode, setPreviewMode] = useState("contents");
  const [pageOptionList, setPageOptionList] = useState([]);
  // const [pageList, setPageList] = useState([]);

  // Click-based content loading state
  const [selectedPage, setSelectedPage] = useState(null);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [isTabSwitching, setIsTabSwitching] = useState(false);
  console.log(pageOptionList, "pageOptionList", selectedPage);
  useEffect(() => {
    if (isWebsite) {
      setPageOptionList(
        pageOptionGenerator({ formData, dynamicpages, websiteJSON, isWebsite })
      );
    } else {
      setPageOptionList(formData?.pageData);
    }
    // setPageOptionList(
    //   pageOptionGenerator(
    //     formData,
    //     selectPage,
    //     dynamicpages,
    //     contentJSON,
    //     isWebsite
    //   )
    // );
  }, [formData, selectPage, dynamicpages, websiteJSON, isWebsite]);

  // Handle component click from iframe
  const handleComponentClick = useCallback((data) => {
    // console.log("🎯 Component clicked:", data);
    setSelectedPage(data?.pageName);
    setSelectedComponent(data?.componentName);
    setPreviewMode("contents");
  }, []);

  // Handle page click from iframe
  const handlePageClick = useCallback((data) => {
    // console.log("📄 Page clicked:", data);
    setSelectedPage(data?.pageName);
    setSelectedComponent(null); // Clear component selection when page is clicked
    setPreviewMode("contents");
  }, []);

  // Memo to stabilize dynamic pages reference (prevents effect loops when undefined)
  const dynPages = useMemo(() => dynamicpages || {}, [dynamicpages]);

  // Persist content and media to formData so tab switches don't clear state
  useEffect(() => {
    // console.log(contentJSON, "contentJSON...");
    setFormData((prev) => ({ ...prev, websiteJSON: websiteJSON }));
  }, [websiteJSON]);

  useEffect(() => {
    // const fileObj = {};
    // Object.keys(fileList)?.map((key) => {
    //   fileObj[`_img-${key}`] = {
    //     ...fileList[key],
    //     url: fileList[key]?.blobUrl,
    //     name: fileList[key]?.originalName,
    //     path: "/assets/" + fileList[key]?.originalName,
    //   };
    // });
    // const fileObj = imageObj({ ...formData?.mediaSet, ...fileList });
    // console.log(fileObj, "fileObj", fileList);
    setFormData((prev) => ({
      ...prev,
      mediaSet: imageObj({ ...prev?.mediaSet, ...fileList }),
    }));
    setReset((prev) => !prev);
  }, [fileList]);

  // Sync with formData changes and reset local state when media operations complete
  useEffect(() => {
    if (formData?.mediaResetTrigger) {
      // Reset local media state when parent triggers reset
      setNewMediaFiles([]);
      setRemoveMediaIds([]);
    }
  }, [formData?.mediaResetTrigger]);

  // Sync local state with formData changes (prevent circular updates)
  useEffect(() => {
    // Only update if values are actually different to prevent circular updates
    if (
      formData?.newMediaFiles !== undefined &&
      JSON.stringify(formData.newMediaFiles) !== JSON.stringify(newMediaFiles)
    ) {
      setNewMediaFiles(formData.newMediaFiles);
    }
    if (
      formData?.removeMediaIds !== undefined &&
      JSON.stringify(formData.removeMediaIds) !== JSON.stringify(removeMediaIds)
    ) {
      setRemoveMediaIds(formData.removeMediaIds);
    }
  }, [formData?.newMediaFiles, formData?.removeMediaIds]); // Only depend on formData, not local state

  // useEffect(() => {
  //   selectPage && fetchPagePreview(selectPage);
  // }, [selectPage]);

  // Collapsible preview state
  const [deviceMode, setDeviceMode] = useState("laptop");
  const [expandedPages, setExpandedPages] = useState(new Set()); // All pages collapsed by default
  const [previewData, setPreviewData] = useState({});
  const [loadingPreviews, setLoadingPreviews] = useState(false);

  // const [selectPage, setSelectPage] = useState(null);
  const templateId = useMemo(() => {
    return template?.id ? `template_${template.id}` : "new_template";
  }, [template?.id]);
  const usedPageIds = useMemo(() => {
    return formData?.pageData?.map((page) => page.id) || [];
  }, [formData?.pageData]);
  // console.log(fileList, "fileList", contentJSON);
  // const debouncedContentJSON = useDebounce(contentJSON, 500);

  // Handle page expand/collapse
  const togglePageExpansion = async (pageVersionId) => {
    const newExpandedPages = new Set(expandedPages);

    if (expandedPages.has(pageVersionId)) {
      // Collapse page
      newExpandedPages.delete(pageVersionId);
    } else {
      // Expand page - fetch preview data if not cached
      newExpandedPages.add(pageVersionId);
      await fetchPagePreview(pageVersionId);
    }

    setExpandedPages(newExpandedPages);
  };

  // Fetch page preview data with caching
  const fetchPagePreview = async (pageVersionId) => {
    const cacheKey = `page_preview_${pageVersionId}`;

    // // Check runtime cache first
    // const cachedData = runtimeCache?.get(cacheKey);
    // const cachedData = runtimeCache.getCachedAvailableTemplatePages(
    //   templateId,
    //   usedPageIds
    // );
    // console.log(cachedData, "cachedData", usedPageIds, templateId);
    // if (cachedData) {
    //   setPreviewData((prev) => ({ ...prev, [pageVersionId]: cachedData }));
    //   return;
    // }

    // Set loading state
    setLoadingPreviews(true);
    if (
      formData?.pageData?.find((pd) => pd?.pageVersionId == pageVersionId)
        ?.isCasheStore
    ) {
      // console.log("already cached");
      // setPreviewData((prev) => ({
      //   ...prev,
      //   [pageVersionId]: finalPageData,
      // }));
      // return;
      setTimeout(() => {
        setReset((prev) => !prev); // removed to avoid unnecessary re-mounts causing loops
        setLoadingPreviews(false);
      }, 1000);
      return;
    }

    try {
      // API call to get page version data

      api.sendRequest(
        apiGenerator(CONSTANTS.API.pageVersions.getById, { id: pageVersionId }),
        (response) => {
          const previewContent = response.data || response;

          const mergeComponentData = previewContent?.componentData?.map(
            (comp) => {
              // Find matching component in template contentJSON
              const matchingComponent = previewContent?.components?.find(
                (tComp) => tComp?.id === comp?.componentVersionId
              );
              const { html, css, js, version } = matchingComponent || {};
              let repeatComponents = [];
              let repeatedPlaceholder = [];
              // console.log(matchingComponent, "matchingComponent");
              if (comp?.repeatComponents && comp?.repeatComponents?.length) {
                repeatComponents = comp?.repeatComponents?.map((rc) => {
                  const matchRepeatComponent = previewContent?.components?.find(
                    (tComp) => tComp?.id == rc?.componentVersionId
                  );
                  const {
                    html: repeatHtml,
                    css,
                    js,
                    version,
                  } = matchRepeatComponent || {};
                  const placeholders = autoDetectPlaceholders(repeatHtml);
                  return { ...rc, ...matchRepeatComponent, placeholders };
                });
                repeatedPlaceholder = autoDetectRepeatPlaceHolder(html);
              }

              const placeholders = autoDetectPlaceholders(html);
              return {
                ...comp,
                html,
                css,
                js,
                componentVersion: version,
                repeatComponents,
                placeholders,
                repeatedPlaceholder,
              };
            }
          );
          const finalPageData = {
            ...previewContent,
            componentData: mergeComponentData,
          };
          // console.log(mergeComponentData, finalPageData, "mergeComponentData");
          // Cache the preview data
          // runtimeCache.set(cacheKey, finalPageData, 300000); // Cache for 5 minutes
          setFormData((pr) => {
            const updatedPages = pr?.pageData?.map((p) =>
              (p?.pageId || p?.id) == finalPageData?.pageId
                ? { ...p, ...finalPageData, id: p?.id, isCasheStore: true }
                : p
            );
            return { ...pr, pageData: updatedPages };
          });
          runtimeCache.updateTemplatePageAvailability(
            finalPageData?.pageId,
            false,
            templateId
          );
          // Update cache to reflect page is no longer available

          // Update preview data state
          // setPreviewData((prev) => ({
          //   ...prev,
          //   [pageVersionId]: finalPageData,
          // }));

          // Remove loading state
          // setLoadingPreviews(false);
        },
        null, // No body data needed for GET request
        null, // No success message
        (error) => {
          console.error(
            `Error fetching preview for page ${pageVersionId}:`,
            error
          );
          message.error(`Failed to load preview for page ${pageVersionId}`);

          // Remove loading state
          setLoadingPreviews(false);
        }
      );
    } catch (error) {
      console.error("Error in fetchPagePreview:", error);
      setLoadingPreviews(false);
    } finally {
      setTimeout(() => {
        // console.log("finally");
        setReset((prev) => !prev); // removed to avoid unnecessary re-mounts causing loops
        setLoadingPreviews(false);
      }, 2000);
      // setReset((prev) => !prev);
      // setLoadingPreviews((prev) => {
      //   const newSet = new Set(prev);
      //   newSet.delete(pageVersionId);
      //   return newSet;
      // });
    }
  };
  // console.log(contentJSON, pageList, selectPage);
  // Ensure a page is selected by default
  useEffect(() => {
    if (!selectPage && formData?.pageData?.length > 0) {
      console.log(formData, "djbjdbjdfdf");
      setSelectPage(formData?.pageData[0]);
      // console.log(formData?.pageData, "selectedPage");
      const pageVersionId = formData?.pageData[0]?.pageVersionId;
      fetchPagePreview(pageVersionId);
    }
    // else if (
    //   !formData?.pageData?.find((pd) => pd?.id == selectPage?.id)?.isCasheStore
    // ) {
    //   console.log(formData?.pageData, "selectedPage 111", selectPage);
    //   setSelectPage(formData?.pageData[0]);
    //   const pageVersionId = formData?.pageData[0]?.pageVersionId;
    //   fetchPagePreview(pageVersionId);
    // }
  }, [selectPage, formData?.pageData]);

  // Build pageList for the selected page and reflect live content/media edits
  // Only rebuild when necessary to avoid performance issues
  useEffect(() => {
    if (!selectPage) return;

    try {
      setLoadingPreviews(true);
      setWebsiteJSON((pr) => {
        const allVariables = JSON.parse(JSON.stringify(pr));
        setPageList((pr) => {
          return templatePageGenerator({
            formData,
            selectPage,
            dynamicpages,
            allVariables,
            template,
          });
        });
        // setFormData((prev) => ({ ...prev, content: allVariables }));
        // console.log(allVariables, "allVariables", pr);
        return allVariables;
      });
    } catch (error) {
      console.log(error, "error");
    } finally {
      setLoadingPreviews(false);
    }
  }, [reset, selectPage?.id, formData?.pageData]); // Add formData.pageData dependency but prevent loops

  // Handle tab switching with loading state
  const handleTabSwitch = useCallback((newMode) => {
    setIsTabSwitching(true);
    setPreviewMode(newMode);
    // Clear loading state when content is ready
    requestAnimationFrame(() => {
      setIsTabSwitching(false);
    });
  }, []);

  const tabContents = {
    contents: {
      key: "contents",
      label: "Content",
      icon: <ListCollapse className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <ClickBasedContentLoader
          contentJSON={websiteJSON}
          setContentJSON={setWebsiteJSON}
          saving={loading}
          setReset={setReset}
          templateObj={formData}
          isLoading={loadingPreviews || isTabSwitching}
          selectedPage={selectedPage}
          selectedComponent={selectedComponent}
          onComponentSelect={setSelectedComponent}
          onPageSelect={setSelectedPage}
          pageList={pageList}
          isImportDoc={isWebsite}
          // setLoadingPreviews={setLoadingPreviews}
        />
      ),
    },
    media: {
      key: "media",
      label: "Media",
      icon: <Image className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <MediaBar
          formData={formData}
          setFormData={setFormData}
          fileList={fileList}
          setFileList={setFileList}
          templateId={templateId}
          newMediaFiles={newMediaFiles}
          setNewMediaFiles={setNewMediaFiles}
          removeMediaIds={removeMediaIds}
          setRemoveMediaIds={setRemoveMediaIds}
          isWebsite={true}
          extraFile={selectedTemplate?.templateMediaSet}
        />
      ),
    },
  };

  const handleSaveContent = async () => {
    try {
      // Update formData with generated content/media
      const updatedFormData = {
        ...formData,
        // full_template_content: fullTemplateContent,
        // templateComponentList: templateComponentList,
        websiteJSON: websiteJSON || [],
        FileList: fileList || [],
        newMediaFiles,
        removeMediaIds,
      };
      handleSubmit(updatedFormData);
    } catch (error) {
      console.error("Error generating template structure:", error);
      message.error("Failed to generate template structure. Please try again.");
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <Card className="mt-6 tw-rounded-2xl tw-border-[#D9DBDF] tw-border tw-text-gray-600">
        <div
          className={`tw-h-screen tw-flex tw-space-x-3 tw-overflow-hidden ${
            isWebsite ? "tw-flex-row-reverse tw-space-x-reverse" : ""
          }`}
        >
          {/* Left Side - Collapsible Preview Section */}
          <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col ">
            {/* Preview Header */}

            <div className="tw-flex tw-items-center tw-justify-center tw-p-2">
              <DeviceButtons
                previewMode={deviceMode}
                setPreviewMode={setDeviceMode}
              />
            </div>

            {/* Collapsible Pages List */}
            <div className="tw-flex-1 tw-overflow-auto tw-p-4 tw-bg-gray-50 tw-rounded-2xl">
              {pageOptionList && pageOptionList?.length > 0 ? (
                <div className="tw-space-y-4">
                  {/* Page Header - Always Visible */}
                  <Select
                    className="tw-w-full"
                    placeholder="Select page"
                    value={selectPage?.name}
                    // value={selectPage?.pageId || selectPage?.id}
                    onChange={(e, option) => {
                      setSelectedComponent(null);
                      setSelectedPage(null);
                      setSelectPage(option?.extra);
                      fetchPagePreview(option?.extra?.pageVersionId);
                    }}
                    options={pageOptionList?.map((pd) => {
                      return {
                        label: pd?.name,
                        value: pd?.name,
                        // value: pd?.pageId || pd?.id,
                        extra: pd,
                      };
                    })}
                  />
                  {/* {pageList && pageList?.length > 0 && ( */}
                  <div className="tw-relative tw-border-t tw-border-gray-200 tw-p-4 tw-bg-gray-50">
                    <SinglePagePreview
                      key={`preview-${selectPage?.id}-${deviceMode}`}
                      templatePage={pageList?.[0]}
                      previewMode={deviceMode}
                      isDragging={false}
                      isLoading={api.isLoading || loadingPreviews}
                      onComponentClick={handleComponentClick}
                      onPageClick={handlePageClick}
                      setLoadingPreviews={setLoadingPreviews}
                      // generatePreview={generatePreview}
                    />
                    {/* )} */}
                  </div>
                  {/* )} */}
                </div>
              ) : (
                <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
                  <div className="tw-text-center">
                    <Eye className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                    <p className="tw-text-gray-500 tw-mb-2">
                      No pages to preview
                    </p>
                    <p className="tw-text-sm tw-text-gray-400">
                      Add pages to your template to see previews here
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Side - Content Editor */}
          <div className="tw-h-full tw-w-full tw-flex tw-flex-col tw-bg-white tw-flex-1">
            {isWebsite && (
              <div className="tw-mb-0">
                <Text className="tw-text-lg" strong>
                  Content
                </Text>
              </div>
            )}
            <TabList
              tabContents={tabContents}
              setPreviewMode={handleTabSwitch}
              previewMode={previewMode}
            />
            <div className="tw-flex-1 tw-flex tw-flex-col tw-overflow-hidden">
              {tabContents[previewMode]?.content ? (
                tabContents[previewMode]?.content
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </Card>
      <div className="tw-flex tw-items-center tw-justify-end tw-space-x-2 tw-mt-4">
        <CommonButton onClick={onCancel} type="text" text="Cancel" />
        <CommonButton
          onClick={handleSaveContent}
          loading={loading}
          disabled={loading}
          text={"Save Website"}
        />
      </div>
    </DndProvider>
  );
};

export default WebsiteContentTab;
