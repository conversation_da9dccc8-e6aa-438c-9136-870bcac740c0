import { <PERSON><PERSON>, <PERSON>, Tabs, Typography, message, Tooltip } from "antd";
import React, {
  useRef,
  useState,
  useCallback,
  useMemo,
  useEffect,
} from "react";
import DynamictabForm from "./DynamictabForm";
import useStorage from "../../../hooks/use-storage";
import { mergePreferExisting } from "./function";

const { Title } = Typography;

// Validate JSON structure for dynamic pages
// const validatePageStructure = (pageData) => {
//   if (!pageData || typeof pageData !== "object") return false;

//   // Check if it has the basic structure
//   const hasValidStructure =
//     typeof pageData.sitemapLabel === "string" &&
//     typeof pageData.navigationType === "string" &&
//     (typeof pageData.columnCount === "number" ||
//       pageData.columnCount === undefined) &&
//     (Array.isArray(pageData.sections) || pageData.sections === undefined);

//   if (!hasValidStructure) return false;

//   // Validate sections if they exist
//   if (pageData.sections) {
//     return pageData.sections.every(
//       (section) =>
//         section &&
//         typeof section.sectionLabel === "string" &&
//         (Array.isArray(section.sectionItems) ||
//           section.sectionItems === undefined) &&
//         (!section.sectionItems ||
//           section.sectionItems.every(
//             (item) =>
//               item &&
//               typeof item.label === "string" &&
//               typeof item.slug === "string"
//           ))
//     );
//   }

//   return true;
// };

const DynamicTab = ({ templatePage, pages = {}, setPages = () => {} }) => {
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const fileInputRef = useRef(null);
  const [activeTab, setActiveTab] = useState(null);
  const formInstancesRef = useRef({});
  const api = useStorage();
  // const [pages, setPages] = useState({});
  // Initialize pages state with default structure

  // console.log(pages, "pages", templatePage);

  // Set active tab to first available page when pages change
  useEffect(() => {
    const pageKeys = Object.keys(pages);
    if (pageKeys.length > 0 && (!activeTab || !pages[activeTab])) {
      setActiveTab(pageKeys[0]);
    }
  }, [pages, activeTab]);
  // console.log(
  //   formInstancesRef.current?.["buses-d"]?.getFieldsValue(),
  //   "formInstancesRef"
  // );
  // Callback to store form instances created by DynamictabForm components
  const handleFormInstanceCreated = useCallback((tabKey, formInstance) => {
    // console.log("Form instance created for tab:", tabKey);
    formInstancesRef.current[tabKey] = formInstance;
  }, []);

  // Import JSON handler
  const handleImportJSON = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // File change handler
  const onFileChange = useCallback(async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      // console.log(parsed, "parsed");
      const processedPages = {};
      const invalidPages = [];
      let mergedPages = {};
      if (parsed) {
        // Handle different JSON structures
        let importedPages = {};

        if (parsed) {
          // If JSON has a 'pages' property, use it
          importedPages = parsed;
        } else if (typeof parsed === "object" && !Array.isArray(parsed)) {
          // If JSON is a direct object with page data, use it as is
          importedPages = parsed;
        }

        // Validate and process imported pages

        Object.entries(importedPages).forEach(([pageName, pageData]) => {
          // console.log(pageData, "pageData", pageName);
          // Validate page structure
          // if (!validatePageStructure(pageData)) {
          //   invalidPages.push(pageName);
          //   return;
          // }

          // Ensure each page has the required structure
          processedPages[pageName] = {
            sitemapLabel: pageData.sitemapLabel || "",
            navigationType: pageData?.navigationType || "Single Column",
            breadcrumbName: pageData?.breadcrumbName || "",
            // staticBreadcrumbName: pageData.breadcrumbName || "",
            columnCount: pageData.columnCount || 0,
            sections: Array.isArray(pageData.sections)
              ? pageData.sections.map((section) => ({
                  sectionLabel: section.sectionLabel || "",
                  sectionItems: Array.isArray(section.sectionItems)
                    ? section.sectionItems.map((item) => ({
                        ...item,
                        label: item.label || "",
                        slug: item.slug ? item.slug.toLowerCase() : "",
                        // sitemapLabel: item?.sitemapLabel || item?.label,
                        extra: item?.extra || {},
                      }))
                    : [],
                }))
              : [],
          };
        });

        // Show warning for invalid pages
        if (invalidPages.length > 0) {
          message.warning(
            `Some pages were skipped due to invalid structure: ${invalidPages.join(
              ", "
            )}`
          );
        }

        // Check if we have any valid pages to import
        if (Object.keys(processedPages).length === 0) {
          message.error(
            "No valid pages found in the JSON file. Please check the file structure. Each page should have 'sitemapLabel' and 'navigationType' fields."
          );
          return;
        }

        // Update pages state
        setPages((prev) => {
          mergedPages = mergePreferExisting(
            prev?.websiteDynamicPageData,
            processedPages
          );

          return {
            ...prev,
            websiteDynamicPageData: mergedPages,
          };
        });
        mergedPages = JSON.parse(
          JSON.stringify(mergePreferExisting(pages, processedPages))
        );

        // Set active tab
        if (parsed.activeTab && mergedPages[parsed.activeTab]) {
          setActiveTab(parsed.activeTab);
        } else {
          // Set to first imported page if no active tab specified
          const firstPageName = Object.keys(mergedPages)[0];
          if (firstPageName) {
            setActiveTab(firstPageName);
          }
        }

        // Force form instances to update with new data
        setTimeout(() => {
          Object.entries(formInstancesRef.current).forEach(
            ([tabKey, formInstance]) => {
              if (mergedPages[tabKey] && formInstance) {
                formInstance.setFieldsValue(mergedPages[tabKey]);
              }
            }
          );
        }, 100);
      }

      message.success(
        `JSON imported successfully! ${
          Object.keys(mergedPages).length
        } page(s) added.`
      );
    } catch (err) {
      console.error("Import JSON error:", err);
      if (err instanceof SyntaxError) {
        message.error("Invalid JSON format. Please check your file syntax.");
      } else {
        message.error(
          "Failed to import JSON. Please check the file format and structure."
        );
      }
    } finally {
      setImporting(false);
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  }, []);

  // Export JSON handler
  const handleExportJSON = useCallback(async () => {
    setExporting(true);
    try {
      const exportData = {
        pages,
        activeTab,
        exportDate: new Date().toISOString(),
        version: "1.0.0",
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `dynamic-pages-${Date.now()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success("Data exported successfully");
    } catch (err) {
      console.error("Export error:", err);
      message.error("Failed to export data");
    } finally {
      setExporting(false);
    }
  }, [pages, activeTab]);

  // Update page data
  const handlePageUpdate = useCallback((tab, newData) => {
    setPages((prev) => ({
      ...prev,
      websiteDynamicPageData: {
        ...prev?.websiteDynamicPageData,
        [tab]: {
          ...prev?.websiteDynamicPageData[tab],
          ...newData,
        },
      },
    }));
  }, []);

  // Cleanup effect
  useEffect(() => {
    return () => {
      formInstancesRef.current = {};
    };
  }, []);

  return (
    <Card className="tw-border tw-rounded-2xl tw-border-[#D9DBDF]">
      <div className="tw-space-y-4">
        <div className="tw-flex tw-justify-between tw-items-center tw-mb-4">
          <div>
            <Title level={4} className="!tw-mb-0 !tw-text-gray-900">
              Dynamic Page Details
            </Title>
          </div>
          <div className="tw-space-x-2">
            <Tooltip title="Import JSON file to auto-generate form fields and populate dynamic page data">
              <Button
                type="primary"
                // icon={<Upload className="tw-w-4 tw-h-4" />}
                onClick={handleImportJSON}
                loading={importing}
                disabled={Object.keys(pages).length == 0}
                className="tw-text-white tw-px-6 tw-h-10 tw-flex tw-items-center tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              >
                {importing ? "Importing..." : "Import JSON"}
              </Button>
            </Tooltip>
            {/* <Button
              type="primary"
              icon={<Download className="tw-w-4 tw-h-4" />}
              onClick={handleExportJSON}
              loading={exporting}
              className="tw-px-6 tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Export JSON
            </Button> */}
            <input
              type="file"
              ref={fileInputRef}
              onChange={onFileChange}
              accept=".json"
              style={{ display: "none" }}
            />
          </div>
        </div>

        {Object.keys(pages).length > 0 && activeTab ? (
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            {Object.entries(pages).map(([tab, pageData]) => (
              <Tabs.TabPane tab={tab} key={tab}>
                <DynamictabForm
                  tab={tab}
                  currentPage={pageData}
                  onValuesChange={(_, allValues) =>
                    handlePageUpdate(tab, allValues)
                  }
                  onFormInstanceCreated={handleFormInstanceCreated}
                />
              </Tabs.TabPane>
            ))}
          </Tabs>
        ) : (
          <div className="tw-text-center tw-py-8 tw-text-gray-500">
            No Dynamic pages
          </div>
        )}
      </div>
    </Card>
  );
};

export default DynamicTab;
