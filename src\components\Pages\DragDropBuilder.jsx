import React, { useState, useEffect, useRef, useCallback } from "react";
import useHttp from "../../hooks/use-http"; // Switch to API hook
// import useStorage from "../../hooks/use-storage"; // Using JSON storage
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator, removeSpacesDeep } from "../../util/functions";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import PageStructure from "./Component/PageStructure";
import PagePreview from "./Component/PagePreview";
import { DND_TYPES } from "../../util/content";
import { generateGlobalPreviewHTML } from "../Components/content";
import PageLibrary from "./Component/PageLibrary";
import { message } from "antd";
import runtimeCache from "../../utils/runtimeCache"; // Runtime cache utility
import {
  createPartialEditPayload,
  hasComponentDataChanged,
} from "../../util/pageDataTransformer";

const DragDropBuilder = ({ page, onSave, onCancel }) => {
  const api = useHttp(); // Switch to API hook
  const { isLoading } = api; // Extract loading state
  const [components, setComponents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pageData, setPageData] = useState({
    name: "",
    urlSlug: "",
    metaTitle: "",
    metaDescription: "",
    wrapperClass: "", // New field
    customCss: "",
    siteMapLabel: "",
    customJs: "",
    headers: "",
    full_page_content: "", // New field: component preview full code
    // pageComponentList: [], // New field: page preview component list
    componentData: [], // Existing field for backward compatibility
  });
  // console.log(pageData, "pageData");

  const [saving, setSaving] = useState(false);
  const [isStructureOpen, setIsStructureOpen] = useState(true);
  const [isComponentOpen, setIsComponentOpen] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(new Set()); // Track which categories are currently loading
  const [isVersionChangeLoading, setIsVersionChangeLoading] = useState(false); // Track version change loading for preview

  // useEffect(() => {
  //   console.log("calingg........");
  //   setFormStructureData(pageData);
  // }, [pageData]);

  // Debouncing effect for search
  useEffect(() => {
    if (searchTerm) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
        setIsSearching(false);
      }, 300); // 300ms debounce delay

      return () => {
        clearTimeout(timer);
      };
    } else {
      setDebouncedSearchTerm("");
      setIsSearching(false);
    }
  }, [searchTerm]);

  // Responsive detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);

      // Auto-close sidebars on mobile for better UX
      if (width < 768) {
        // On mobile, close both sidebars by default
        setIsComponentOpen(false);
        setIsStructureOpen(false);
      } else if (width >= 768 && width < 1024) {
        // On tablet, allow one sidebar open
        // Keep current state but ensure at least center content is visible
      }
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Load categories on mount with runtime caching
  useEffect(() => {
    // Check if categories are cached first
    // console.log("fgfgfg", debouncedSearchTerm);
    const cachedCategories = runtimeCache.getCachedCategories();
    // if (cachedCategories && !debouncedSearchTerm) {
    //   console.log("Categories loaded from runtime cache");
    //   setCategories(cachedCategories);
    //   return;
    // }
    // Fetch from API if not cached
    api.sendRequest(
      CONSTANTS.API.categories.getCategoryDropDownList,
      (res) => {
        console.log("Categories fetched from API:", res);
        const categories = res?.data?.rows || res?.rows || res || [];
        setCategories(categories);

        // Cache the categories for future use
        runtimeCache.cacheCategories(categories);
      },
      { search: debouncedSearchTerm || "" },
      null,
      (error) => {
        console.error("Error loading categories:", error);
        message.error(
          "Failed to load categories. Please check your connection."
        );
      }
    );
  }, [debouncedSearchTerm]);

  useEffect(() => {
    if (page) {
      // Ensure existing components have uniqueId for stable React keys
      const componentsWithUniqueId = (page?.componentData || [])?.map(
        (comp, index) => ({
          ...comp,
          class: comp?.class,
          uniqueId:
            comp?.uniqueId ||
            `${comp?.id}-${index}-${Date.now()}-${Math.random()
              .toString(36)
              .substring(2, 11)}`,
        })
      );
      // console.log(page, "page in useEffect");
      setPageData({
        name: page?.name || "",
        urlSlug: page?.urlSlug || "",
        metaTitle: page?.metaTitle || "",
        metaDescription: page?.metaDescription || "",
        wrapperClass: page?.wrapperClass || "", // New field
        siteMapLabel: page?.siteMapLabel || "",
        customCss: page?.customCss || "",
        customJs: page?.customJs || "",
        headers: page?.headers || "",
        full_page_content: page?.full_page_content || "", // New field
        // pageComponentList: page?.pageComponentList || [], // New field
        componentData: componentsWithUniqueId,
      });
    }
  }, [page]);

  const handleSave = async () => {
    // Validate required fields
    const validationErrors = [];

    if (!pageData?.name || pageData?.name?.trim() === "") {
      validationErrors?.push("Page name is required");
    }

    if (!pageData?.urlSlug || pageData?.urlSlug?.trim() === "") {
      validationErrors?.push("URL slug is required");
    }

    // Validate URL slug format (URL-friendly characters only)
    if (pageData?.urlSlug && !/^[a-z0-9-_]+$/i.test(pageData.urlSlug)) {
      validationErrors.push(
        "URL slug can only contain letters, numbers, hyphens, and underscores"
      );
    }

    // Validate unique component names
    const componentNames = pageData?.componentData
      ?.map((comp) => comp.name)
      .filter((name) => name && name.trim() !== "");
    const uniqueNames = new Set(componentNames);
    if (componentNames.length !== uniqueNames.size) {
      validationErrors.push("Component name must be unique.");
    }

    // Show validation errors as notifications
    if (validationErrors.length > 0) {
      // validationErrors.forEach((error) => {
      message.error(validationErrors[0]);
      // message.error);
      // });
      return; // Don't proceed with save
    }

    setSaving(true);

    const apiConfig = page
      ? apiGenerator(CONSTANTS.API.pages.update, { id: page.id })
      : CONSTANTS.API.pages.create;
    const payload = {
      name: pageData?.name,
      urlSlug: pageData?.urlSlug,
      metaTitle: pageData?.metaTitle,
      metaDescription: pageData?.metaDescription,
      siteMapLabel: pageData?.siteMapLabel,
      wrapperClass: pageData?.wrapperClass,
      customCss: pageData?.customCss,
      customJs: pageData?.customJs,
      headers: pageData?.headers,
      componentData: pageData?.componentData?.map((el, index) => ({
        index: index,
        name: el?.name,
        class: el?.class,
        type: el?.type,
        componentId: el?.componentId,
        componentVersionId: el?.componentVersionId,
        componentVersion: el?.version,
        ...(el?.repeatComponents?.length
          ? {
              repeatComponents: el?.repeatComponents?.map((rc) => ({
                key: rc?.key,
                componentVersionId: rc?.versionId,
                componentId: rc?.componentId,
                componentVersion: rc?.version,
                componentName: rc?.componentName,
              })),
            }
          : {}),
      })),
    };
    // For updates, use the new data transformation utility for partial payload
    let payloadToSend = payload;
    if (page) {
      try {
        // Use the new utility function to create partial payload
        const partialPayload = createPartialEditPayload(page, payload);

        // Always include the page ID for updates
        payloadToSend = {
          // id: page.id,
          ...partialPayload,
        };

        // Log what's being sent for debugging
        // console.log("Enhanced partial update payload:", payloadToSend);
        // console.log("Original page data:", page);
        // console.log("New page data:", payload);
      } catch (transformError) {
        console.error(
          "Error creating partial payload, using fallback:",
          transformError
        );

        // Fallback to original logic if transformation fails
        const changed = { id: page.id };
        const keys = Object.keys(payload);
        let componentDataChanged = false;

        for (const k of keys) {
          try {
            const currentValue = JSON.stringify(payload[k]);
            const originalValue = JSON.stringify(page[k]);

            if (currentValue !== originalValue) {
              changed[k] = payload[k];

              // Track if componentData has changed
              if (k === "componentData") {
                componentDataChanged = true;
                // console.log(
                //   "ComponentData has changed (fallback), including full array in payload"
                // );
              }
            }
          } catch (e) {
            // Fallback shallow compare
            if (payload[k] !== page[k]) {
              changed[k] = payload[k];

              // Track if componentData has changed
              if (k === "componentData") {
                componentDataChanged = true;
                // console.log(
                //   "ComponentData has changed (fallback shallow), including full array in payload"
                // );
              }
            }
          }
        }

        payloadToSend = removeSpacesDeep(changed);
        message.warning("Using fallback payload construction method.");
      }
    }

    api.sendRequest(
      apiConfig,
      (res) => {
        // console.log("Page saved successfully:", res);
        setSaving(false);
        const returned = res?.data;
        const updatedItem =
          returned && returned.id !== undefined
            ? { ...returned, type: page ? "update" : "" }
            : { ...pageData, id: page?.id, type: page ? "update" : "" };
        onSave(updatedItem);
      },
      payloadToSend,
      page ? "Page updated successfully!" : "Page created successfully!",
      (error) => {
        console.error("Error saving page:", error);
        message.error(error || "Failed to save page. Please try again.");
        setSaving(false);
      }
    );
  };

  const generatePreviewHTML = () => {
    setIsVersionChangeLoading(true); // Set loading state to true before generating preview
    try {
      // Process components and replace {{key}} placeholders with actual component data
      const processedComponents = pageData?.componentData?.map((component) => {
        if (component.type === "repeat" && component?.repeatComponents) {
          // Create a copy of the component to avoid mutating original
          let processedComponent = { ...component };

          // Replace {{key}} placeholders in component HTML with actual component data
          if (component?.html) {
            let processedHTML = component?.html;

            // Iterate through all repeat components and replace their placeholders
            component?.repeatComponents?.forEach((repeatComp) => {
              if (repeatComp?.key && repeatComp?.html) {
                // Replace the placeholder with the actual component HTML
                const regex = new RegExp(`\\{\\{${repeatComp?.key}\\}\\}`, "g");
                processedHTML = processedHTML?.replace(regex, repeatComp?.html);
              }
            });

            processedComponent.html = processedHTML;
          }

          return processedComponent;
        }

        return component;
      });

      const result = generateGlobalPreviewHTML({
        type: "page",
        data: processedComponents,
        pageData,
        // components,
      });

      return result;
    } catch (error) {
      console.error("Error generating preview HTML:", error);
      throw error; // Re-throw the error to be handled by the caller
    } finally {
      setIsVersionChangeLoading(false); // Set loading state to false whether successful or not
    }
  };

  // Reorder helper for immediate component moves
  const moveComponent = (fromIndex, toIndex) => {
    // console.log(`Moving component from ${fromIndex} to ${toIndex}`);
    const updatedComponents = [...pageData?.componentData];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents?.splice(toIndex, 0, movedComponent);
    // inside index id also change
    updatedComponents?.forEach((comp, index) => {
      if (comp) {
        comp.index = index;
      }
    });
    // console.log(updatedComponents);
    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
      // pageComponentList: updatedComponents,
    }));
  };

  const removeComponentFromPage = (index) => {
    // console.log("Removing com/ponent at index:", index, pageData?.componentData);
    const updatedComponents =
      pageData && pageData.componentData
        ? pageData.componentData.filter((data, i) => i != index)
        : [];

    setPageData((prevData) => ({
      ...prevData,
      componentData: updatedComponents,
      // pageComponentList: updatedComponents,
    }));
  };

  // Update CSS class for a specific component in page structure
  const handleCssChange = (index, value) => {
    const updated = [...pageData?.componentData];
    updated[index] = { ...updated[index], css: value };

    setPageData((prevData) => ({
      ...prevData,
      components: updated,
    }));
  };

  // Global onChange handler for all component fields
  const handleComponentFieldChange = (index, field, value, extraData = {}) => {
    const updated = [...pageData?.componentData];
    updated[index] = { ...updated[index], [field]: value, ...extraData };
    // console.log(updated, "updated");
    setPageData((prevData) => ({
      ...prevData,
      componentData: updated,
      // pageComponentList: updated,
    }));
  };
  // console.log(components, "all components");
  const groupedComponents = components.reduce((acc, comp) => {
    const category = comp?.categoryData;
    if (!category) return acc;
    // Apply search filter if needed
    if (
      debouncedSearchTerm &&
      !comp?.name?.toLowerCase()?.includes(debouncedSearchTerm?.toLowerCase())
    ) {
      return acc;
    }
    // If category not yet created in accumulator → init
    if (!acc[category.id]) {
      acc[category.id] = {
        name: category?.name,
        colour: category?.colour,
        components: [],
      };
    }
    // Push component into category bucket
    acc[category.id].components.push(comp);

    return acc;
  }, {});
  // console.log(components, pageData, "hdfgjdf");
  // Function to load components for a specific category with runtime caching
  const loadComponentsByCategory = useCallback(
    (categoryId) => {
      // Check if this category is already loading
      if (loadingCategories.has(categoryId)) {
        return; // Already loading, no need to make another API call
      }

      // Check if components are already cached in runtime cache
      const cachedCategoryComponents =
        runtimeCache.getCachedComponentsByCategory(categoryId);
      if (cachedCategoryComponents) {
        // console.log(
        //   `Components loaded from runtime cache for category ${categoryId}`
        // );

        // Add category data to cached components
        const componentsWithCategory = cachedCategoryComponents.map((comp) => ({
          ...comp,
          version: comp?.componentversions?.reduce((acc, v) => {
            return acc?.version > v?.version ? acc : v;
          }),
          categoryData: categories?.find((cat) => cat?.id == categoryId),
        }));

        // Add cached components to state
        setComponents((prevComponents) => {
          // Remove any existing components from this category
          const filteredComponents = prevComponents.filter(
            (c) => !c.categoryData || c.categoryData.id != categoryId
          );
          // Add all cached components from this category
          return [...filteredComponents, ...componentsWithCategory];
        });
        return; // Exit early since we have cached data
      }

      // Add category to loading state
      setLoadingCategories((prev) => new Set([...prev, +categoryId]));

      // Fetch components from API
      api.sendRequest(
        CONSTANTS.API.components.getAllForPage,
        (res) => {
          // console.log(
          //   `Components fetched from API for category ${categoryId}:`,
          //   res
          // );
          const categoryComponents = res?.data?.rows || [];

          // Cache the components in runtime cache
          runtimeCache.cacheComponentsByCategory(
            categoryId,
            categoryComponents
          );

          // Add category data to each component for grouping
          const componentsWithCategory = categoryComponents?.map((comp) => ({
            ...comp,
            version: comp?.componentversions?.reduce((acc, v) => {
              return acc?.version > v?.version ? acc : v;
            }),
            categoryData: categories?.find((cat) => cat.id == +categoryId),
          }));

          // Update components state with fresh data
          setComponents((prevComponents) => {
            // Remove any existing components from this category
            const filteredComponents = prevComponents.filter(
              (c) => !c?.categoryData || c?.categoryData.id != +categoryId
            );
            // Add all fresh components from this category
            return [...filteredComponents, ...componentsWithCategory];
          });

          // Remove category from loading state (success)
          setLoadingCategories((prev) => {
            const newSet = new Set(prev);
            newSet.delete(+categoryId);
            return newSet;
          });
        },
        { categoryId }, // Send categoryId as query parameter
        null,
        (error) => {
          console.error(
            `Error loading components for category ${categoryId}:`,
            error
          );

          // Remove category from loading state (error)
          setLoadingCategories((prev) => {
            const newSet = new Set(prev);
            newSet.delete(+categoryId);
            return newSet;
          });

          message.error(
            `Failed to load components for this category. Please check your connection.`
          );
        }
      );
    },
    [api, categories, loadingCategories]
  );

  const handleGlobalFieldChange = (id, field, value) => {
    // version change time preview update
    setComponents((prev) => {
      return prev?.map((comp) => {
        if (comp?.id == id) {
          return { ...comp, [field]: value };
        }
        return comp;
      });
    });
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden tw-relative">
        {/* Mobile Backdrop */}
        {isMobile && (isComponentOpen || isStructureOpen) && (
          <div
            className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-z-40"
            onClick={() => {
              setIsComponentOpen(false);
              setIsStructureOpen(false);
            }}
          />
        )}

        {/* Left Sidebar - Components Library */}
        <PageLibrary
          isComponentOpen={isComponentOpen}
          setIsComponentOpen={setIsComponentOpen}
          isMobile={isMobile}
          isTablet={isTablet}
          type={DND_TYPES.LIB_ITEM}
          listData={groupedComponents}
          categories={categories}
          loadComponentsByCategory={loadComponentsByCategory}
          loadingCategories={loadingCategories}
          isLoading={isSearching}
          onSearchChange={(e) => setSearchTerm(e)}
          handleGlobalFieldChange={handleGlobalFieldChange}
          // LibraryItem={LibraryItem}
        />

        <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col">
          <PagePreview
            pageData={pageData}
            setPageData={setPageData}
            components={components}
            handleSave={handleSave}
            saving={saving}
            onCancel={onCancel}
            isStructureOpen={isStructureOpen}
            setIsStructureOpen={setIsStructureOpen}
            isComponentOpen={isComponentOpen}
            setIsComponentOpen={setIsComponentOpen}
            generatePreviewHTML={generatePreviewHTML}
            isVersionChangeLoading={isVersionChangeLoading}
          />
        </div>

        <PageStructure
          isStructureOpen={isStructureOpen}
          setIsStructureOpen={setIsStructureOpen}
          pageData={pageData}
          setPageData={setPageData}
          components={components}
          handleCssChange={handleCssChange}
          removeComponentFromPage={removeComponentFromPage}
          moveComponent={moveComponent}
          onComponentFieldChange={handleComponentFieldChange}
          onVersionChangeLoading={setIsVersionChangeLoading}
        />
      </div>
    </DndProvider>
  );
};

export default DragDropBuilder;
