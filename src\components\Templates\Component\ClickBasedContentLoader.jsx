import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect,
} from "react";
import SearchBar from "../../common/SearchBar";
import { Button, Input, message, Tag, Divider, Spin, Card } from "antd";
import JoditTextEditor from "../../common/JoditTextEditor";
import mammoth from "mammoth";
import {
  addAtPath,
  deleteAtPath,
  parseContent,
  searchKeysDeep,
  updateAtPath,
} from "../../../util/functions";
import { deepMerge } from "../../FeaturePage/components/function";
import { prePareContent } from "../../../utils/Content";
import { Trash2, GripVertical, Eye, EyeOff } from "lucide-react";
import useDebounce from "../../../hooks/useDebounce";

const { TextArea } = Input;

const ClickBasedContentLoader = ({
  contentJSON,
  setContentJSON,
  saving,
  setReset,
  templateObj = {},
  dynamicContentForm = {},
  isImportDoc = false,
  isLoading = false,
  selectedPage = null,
  selectedComponent = null,
  onComponentSelect,
  onPageSelect,
  pageList,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [importing, setImporting] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [isUpdatingContent, setIsUpdatingContent] = useState(false);
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isTextEditorLoading, setIsTextEditorLoading] = useState(false);
  const [textEditorReady, setTextEditorReady] = useState(false);
  const fileInputRef = useRef(null);
  const fileInputExportRef = useRef(null);
  const originalValuesRef = useRef(new Map());
  const [localTextAreaValues, setLocalTextAreaValues] = useState(new Map());
  const [textAreaErrors, setTextAreaErrors] = useState(new Map());
  // console.log(templateObj, "templateObj");
  // Content caching to prevent unnecessary re-renders
  const contentCacheRef = useRef(new Map());
  const searchDebounce = useDebounce(searchTerm, 300);
  // Helper function to check if content has actually changed
  const hasContentChanged = useCallback((key, newValue) => {
    const originalValue = originalValuesRef.current.get(key);
    return originalValue !== newValue;
  }, []);

  // Helper function to store original value
  const storeOriginalValue = useCallback((key, value) => {
    originalValuesRef.current.set(key, value);
  }, []);

  // Helper function to get local TextArea value
  const getLocalTextAreaValue = useCallback(
    (key, defaultValue) => {
      return localTextAreaValues.get(key) ?? defaultValue;
    },
    [localTextAreaValues]
  );

  // Helper function to set local TextArea value
  const setLocalTextAreaValue = useCallback((key, value) => {
    setLocalTextAreaValues((prev) => {
      const newMap = new Map(prev);
      newMap.set(key, value);
      return newMap;
    });
  }, []);

  // Validation function to check for spaces in the middle of words
  const validateTextAreaContent = useCallback((text) => {
    if (!text || typeof text !== "string") return { isValid: true, error: "" };

    // Check if there are spaces in the middle of words
    // This regex matches any word character followed by a space followed by another word character
    const hasSpaceInMiddle = /\w\s+\w/.test(text);

    if (hasSpaceInMiddle) {
      return {
        isValid: false,
        error: "Spaces are not allowed",
      };
    }

    return { isValid: true, error: "" };
  }, []);

  // Helper function to get textarea error
  const getTextAreaError = useCallback(
    (key) => {
      return textAreaErrors.get(key) || "";
    },
    [textAreaErrors]
  );

  // Helper function to set textarea error
  const setTextAreaError = useCallback((key, error) => {
    setTextAreaErrors((prev) => {
      const newMap = new Map(prev);
      if (error) {
        newMap.set(key, error);
      } else {
        newMap.delete(key);
      }
      return newMap;
    });
  }, []);

  // Helper function to check if a specific key has image keys
  const hasImageKeyForField = useCallback(
    (pageName, componentName, fieldKey) => {
      if (!pageList || !pageList[0] || !pageList[0].imgVariable) return false;
      const imgVariable = pageList[0].imgVariable;
      const componentImgKeys = imgVariable[componentName];

      if (!componentImgKeys) return false;

      // Check if the specific field key exists in the component's image keys
      const hasImageKey = fieldKey in componentImgKeys;
      // console.log("🔍 Checking specific field image key:", {
      //   pageName,
      //   componentName,
      //   fieldKey,
      //   componentImgKeys,
      //   hasImageKey,
      //   fullPath: [pageName, componentName, fieldKey],
      // });
      return hasImageKey;
    },
    [pageList]
  );

  // Clear original values and cache when contentJSON changes significantly
  useEffect(() => {
    originalValuesRef.current.clear();
    contentCacheRef.current.clear();
    setLocalTextAreaValues(new Map()); // Clear local TextArea values
    setTextAreaErrors(new Map()); // Clear textarea errors
  }, [contentJSON]);

  // Handle content loading when selection changes
  useEffect(() => {
    if (selectedPage || selectedComponent) {
      setIsContentLoading(true);
      setIsTextEditorLoading(true);
      setTextEditorReady(false);
    } else {
      setIsContentLoading(false);
      setIsTextEditorLoading(false);
      setTextEditorReady(false);
    }
  }, [selectedPage, selectedComponent]);

  const handleSearch = useCallback((value) => {
    setSearchTerm(value);
  }, []);

  const handleDelete = useCallback((path, fieldKey = null) => {
    try {
      setContentJSON((prev) => deleteAtPath(prev, path, fieldKey));
      message.success("Item deleted successfully");
    } catch (e) {
      console.error("Error in handleDelete:", e);
      message.error("Failed to delete item");
    }
  }, []);

  const contentChangeHandler = ({ variableName, value, e, path }) => {
    if (path?.length < 2) return console.error("path is less than 2");

    const editorKey = [...path, variableName].join("_");

    if (!hasContentChanged(editorKey, value)) {
      return;
    }

    setIsUpdatingContent(true);
    setContentJSON((prev) => {
      const newValue = JSON.parse(JSON.stringify(prev));
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      current[variableName] = value;
      return newValue;
    });
    setReset((prev) => !prev);

    storeOriginalValue(editorKey, value);

    setTimeout(() => {
      setIsUpdatingContent(false);
    }, 300);
  };

  const addItemHandler = useCallback(
    (tempObj, path, key) => {
      setIsUpdatingContent(true);
      setContentJSON((prev) => {
        const newValue = JSON.parse(JSON.stringify(prev));
        let currentpage = tempObj?.pageData?.find(
          (page) => page?.name?.trim() == path[0]?.trim()
        );
        let currentCom = currentpage?.componentData?.find(
          (com) => com.name?.trim() == path[1]?.trim()
        );
        let current = newValue;
        path?.map((key) => {
          current = current[key] || {};
        });
        const intialPlaceholder = {};
        console.log(currentCom, "currentCom", currentpage, path, key, tempObj);
        if (currentCom?.repeatComponents?.length) {
          currentCom?.repeatComponents?.map((rc) => {
            rc?.placeholders?.map((placeholder) => {
              intialPlaceholder[placeholder] = "";
            });
          });
        } else {
          console.warn(
            `repeatComponents or placeholders not found for component: ${currentCom?.name}`
          );
          intialPlaceholder["content"] = "";
        }

        current[key].push(intialPlaceholder);
        return newValue;
      });
      setReset((prev) => !prev);

      setTimeout(() => {
        setIsUpdatingContent(false);
      }, 300);
    },
    [templateObj, setContentJSON, setReset]
  );

  const handleAddItem = useCallback(
    (path, key, tempObj) => {
      setIsAddingItem(true);
      try {
        addItemHandler(tempObj, path, key);
        message.success("Item added");
      } catch (e) {
        console.error("Error in handleAddItem:", e);
        message.error("Failed to add item");
      } finally {
        setTimeout(() => {
          setIsAddingItem(false);
        }, 500);
      }
    },
    [addItemHandler, templateObj]
  );

  const deleteItemHandler = (path, index) => {
    setContentJSON((prev) => {
      const newValue = JSON.parse(JSON.stringify(prev));
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      current.splice(index, 1);
      return newValue;
    });
    setReset((prev) => !prev);
  };

  const buildItemList = (data, path = []) => {
    if (Array.isArray(data)) {
      return data.map((item, index) => (
        <div key={[...path, index].join("_")} className="tw-mb-4">
          <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
            <div className="tw-flex tw-items-center tw-space-x-2">
              <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
              <span className="tw-font-medium tw-text-gray-700">
                Position {index + 1}
              </span>
            </div>
            <Button
              type="text"
              danger
              size="small"
              icon={<Trash2 className="tw-w-4 tw-h-4" />}
              onClick={() => deleteItemHandler([...path], index)}
            />
          </div>
          {typeof item === "object" && item !== null ? (
            <div className="tw-ml-4">
              {buildItemList(item, [...path, index])}
            </div>
          ) : (
            <div className="tw-relative">
              {isTextEditorLoading && !textEditorReady && (
                <div className="tw-absolute tw-inset-0 tw-bg-white tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-z-10 tw-rounded">
                  <Spin size="small" />
                </div>
              )}
              {textEditorReady && (
                <>
                  {/* Check if this specific field has image keys - show text area */}
                  {hasImageKeyForField(path[0], path[1], path[2]) ? (
                    <>
                      <TextArea
                        defaultValue={item}
                        placeholder="Enter your content here..."
                        key={[...path, index].join("_")}
                        rows={4}
                        status={
                          getTextAreaError([...path, index].join("_"))
                            ? "error"
                            : undefined
                        }
                        className="tw-w-full"
                        onBlur={(e) => {
                          const editorKey = [...path, index].join("_");
                          const newContent = e.target.value?.trim();

                          // First check for space validation
                          const validation =
                            validateTextAreaContent(newContent);

                          if (!validation.isValid) {
                            setTextAreaError(editorKey, validation.error);
                            return;
                          } else {
                            setTextAreaError(editorKey, "");
                          }

                          if (!originalValuesRef.current.has(editorKey)) {
                            storeOriginalValue(editorKey, item);
                          }

                          if (!hasContentChanged(editorKey, newContent)) {
                            return;
                          }

                          setIsUpdatingContent(true);
                          setContentJSON((prev) =>
                            updateAtPath(
                              prev,
                              [...path, index],
                              () => newContent
                            )
                          );

                          storeOriginalValue(editorKey, newContent);

                          requestAnimationFrame(() => {
                            setIsUpdatingContent(false);
                          });
                        }}
                      />
                      {getTextAreaError([...path, index].join("_")) && (
                        <div className="tw-text-red-500 tw-text-sm tw-mt-1">
                          {getTextAreaError([...path, index].join("_"))}
                        </div>
                      )}
                    </>
                  ) : (
                    /* Show text editor for non-image components */
                    <JoditTextEditor
                      value={item}
                      key={[...path, index].join("_")}
                      height="100"
                      placeholder="Enter your content here..."
                      onBlur={(newContent) => {
                        const content = newContent?.trim();
                        const editorKey = [...path, index].join("_");

                        if (!originalValuesRef.current.has(editorKey)) {
                          storeOriginalValue(editorKey, item);
                        }

                        if (!hasContentChanged(editorKey, content)) {
                          return;
                        }

                        setIsUpdatingContent(true);
                        setContentJSON((prev) =>
                          updateAtPath(prev, [...path, index], () => content)
                        );

                        storeOriginalValue(editorKey, content);

                        // Use requestAnimationFrame instead of setTimeout
                        requestAnimationFrame(() => {
                          setIsUpdatingContent(false);
                        });
                      }}
                    />
                  )}
                </>
              )}
            </div>
          )}
        </div>
      ));
    } else if (typeof data === "object" && data !== null) {
      return Object.keys(data).map((key) => (
        <div key={[...path, key].join("_")} className="tw-mb-4">
          <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
            <div className="tw-flex tw-items-center tw-space-x-2">
              <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
              <span className="tw-font-medium tw-text-gray-700">{key}</span>
              {Array.isArray(data[key]) && (
                <Tag className="tw-p-1 tw-px-2 tw-rounded-xl" color="purple">
                  Repeat
                </Tag>
              )}
            </div>
          </div>
          {typeof data[key] === "object" && data[key] !== null ? (
            <>
              <div className="tw-ml-4">
                {buildItemList(data[key], [...path, key])}
              </div>
              {Array.isArray(data[key]) && (
                <div className="tw-mt-2 tw-w-full tw-flex tw-justify-center tw-items-center">
                  <Button
                    type="dashed"
                    className="tw-w-full"
                    onClick={() => handleAddItem([...path], key, templateObj)}
                    loading={isAddingItem}
                    disabled={isAddingItem || isUpdatingContent}
                  >
                    {isAddingItem ? "Adding Item..." : "+ Add Item"}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="tw-relative">
              {isTextEditorLoading && !textEditorReady && (
                <div className="tw-absolute tw-inset-0 tw-bg-white tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-z-10 tw-rounded">
                  <Spin size="small" />
                </div>
              )}
              {textEditorReady && (
                <>
                  {/* Check if this specific field has image keys - show text area */}
                  {hasImageKeyForField(path[0], path[1], key) ? (
                    <>
                      <TextArea
                        defaultValue={data[key]}
                        placeholder="Enter your content here..."
                        key={[...path, key].join("_")}
                        rows={4}
                        status={
                          getTextAreaError([...path, key].join("_"))
                            ? "error"
                            : undefined
                        }
                        className="tw-w-full"
                        onBlur={(e) => {
                          const editorKey = [...path, key].join("_");
                          const newContent = e.target.value?.trim();

                          // First check for space validation
                          const validation =
                            validateTextAreaContent(newContent);

                          if (!validation.isValid) {
                            setTextAreaError(editorKey, validation.error);
                            return;
                          } else {
                            setTextAreaError(editorKey, "");
                          }

                          if (!originalValuesRef.current.has(editorKey)) {
                            storeOriginalValue(editorKey, data[key]);
                          }

                          if (!hasContentChanged(editorKey, newContent)) {
                            return;
                          }

                          contentChangeHandler({
                            variableName: key,
                            value: newContent,
                            path,
                          });
                        }}
                      />
                      {getTextAreaError([...path, key].join("_")) && (
                        <div className="tw-text-red-500 tw-text-sm tw-mt-1">
                          {getTextAreaError([...path, key].join("_"))}
                        </div>
                      )}
                    </>
                  ) : (
                    /* Show text editor for non-image components */
                    <JoditTextEditor
                      value={data[key]}
                      height="100"
                      placeholder="Enter your content here..."
                      key={[...path, key].join("_")}
                      onBlur={(newContent) => {
                        const content = newContent?.trim();
                        const editorKey = [...path, key].join("_");

                        if (!originalValuesRef.current.has(editorKey)) {
                          storeOriginalValue(editorKey, data[key]);
                        }

                        if (!hasContentChanged(editorKey, content)) {
                          return;
                        }

                        contentChangeHandler({
                          variableName: key,
                          value: content,
                          path,
                        });
                      }}
                    />
                  )}
                </>
              )}
            </div>
          )}
        </div>
      ));
    } else {
      return (
        <div className="tw-relative">
          {isTextEditorLoading && !textEditorReady && (
            <div className="tw-absolute tw-inset-0 tw-bg-white tw-bg-opacity-50 tw-flex tw-items-center tw-justify-center tw-z-10 tw-rounded">
              <Spin size="small" />
            </div>
          )}
          {textEditorReady && (
            <>
              {/* Check if this specific field has image keys - show text area */}
              {hasImageKeyForField(path[0], path[1], path[2]) ? (
                <>
                  <TextArea
                    defaultValue={data}
                    placeholder="Enter your content here..."
                    key={path.join("_")}
                    rows={4}
                    status={
                      getTextAreaError(path.join("_")) ? "error" : undefined
                    }
                    className="tw-w-full"
                    onBlur={(e) => {
                      const editorKey = path.join("_");
                      const newContent = e.target.value?.trim();

                      // First check for space validation
                      const validation = validateTextAreaContent(newContent);

                      if (!validation.isValid) {
                        setTextAreaError(editorKey, validation.error);
                        return;
                      } else {
                        setTextAreaError(editorKey, "");
                      }

                      if (!originalValuesRef.current.has(editorKey)) {
                        storeOriginalValue(editorKey, data);
                      }

                      if (!hasContentChanged(editorKey, newContent)) {
                        return;
                      }

                      setIsUpdatingContent(true);
                      setContentJSON((prev) =>
                        updateAtPath(prev, path, () => newContent)
                      );

                      storeOriginalValue(editorKey, newContent);

                      requestAnimationFrame(() => {
                        setIsUpdatingContent(false);
                      });
                    }}
                  />
                  {getTextAreaError(path.join("_")) && (
                    <div className="tw-text-red-500 tw-text-sm tw-mt-1">
                      {getTextAreaError(path.join("_"))}
                    </div>
                  )}
                </>
              ) : (
                /* Show text editor for non-image components */
                <JoditTextEditor
                  value={data}
                  height="100"
                  key={path.join("_")}
                  placeholder="Enter your content here..."
                  onBlur={(newContent) => {
                    const content = newContent?.trim();
                    const editorKey = path.join("_");

                    if (!originalValuesRef.current.has(editorKey)) {
                      storeOriginalValue(editorKey, data);
                    }

                    if (!hasContentChanged(editorKey, content)) {
                      return;
                    }

                    setIsUpdatingContent(true);
                    setContentJSON((prev) =>
                      updateAtPath(prev, path, () => content)
                    );

                    storeOriginalValue(editorKey, content);

                    // Use requestAnimationFrame instead of setTimeout
                    requestAnimationFrame(() => {
                      setIsUpdatingContent(false);
                    });
                  }}
                />
              )}
            </>
          )}
        </div>
      );
    }
  };

  const handleImportJSON = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const onFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      if (!parsed || typeof parsed !== "object" || Array.isArray(parsed)) {
        throw new Error("Invalid JSON structure. Expected an object at root.");
      }
      setContentJSON((pr) => {
        // console.log(deepMerge(pr, parsed), "deepMerge", parsed, pr);
        return deepMerge(pr, parsed);
      });
      message.success("JSON imported successfully");
    } catch (err) {
      console.error("Import JSON error:", err);
      message.error("Failed to import JSON. Please check the file.");
    } finally {
      setImporting(false);
      setReset((prev) => !prev);
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };

  // Filter content based on selected page and component with caching
  const filteredContent = useMemo(() => {
    if (!contentJSON) return {};
    if (!selectedPage) return {};

    const cacheKey = `${selectedPage}_${
      selectedComponent || "all"
    }_${searchDebounce}`;

    // Check cache first
    if (contentCacheRef.current.has(cacheKey)) {
      const cached = contentCacheRef.current.get(cacheKey);
      // Verify cache is still valid by checking if contentJSON has changed
      if (cached.timestamp > Date.now() - 5000) {
        // 5 second cache
        return cached.data;
      }
    }

    const pageContent = contentJSON[selectedPage] || {};
    let result;

    if (!selectedComponent) {
      result = { [selectedPage]: pageContent };
    } else {
      result = {
        [selectedPage]: {
          [selectedComponent]: pageContent[selectedComponent] || {},
        },
      };
    }
    result = searchKeysDeep(result, searchDebounce);
    // console.log(result, "result", searchDebounce);

    // Cache the result
    contentCacheRef.current.set(cacheKey, {
      data: result,
      timestamp: Date.now(),
    });

    return result;
  }, [contentJSON, selectedPage, selectedComponent, searchDebounce]);

  // Handle content ready state
  useEffect(() => {
    const hasContent =
      filteredContent && Object.keys(filteredContent).length > 0;
    if (hasContent && (selectedPage || selectedComponent)) {
      setIsContentLoading(false);
      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(() => {
        setTextEditorReady(true);
        setIsTextEditorLoading(false);
      });
    }
  }, [filteredContent, selectedPage, selectedComponent]);

  const itemList = useMemo(() => {
    if (!filteredContent || Object.keys(filteredContent).length === 0)
      return [];

    const pages = Object.keys(filteredContent).map((pageKey) => {
      const pageData = filteredContent[pageKey];
      const components = Object.keys(pageData).map((componentKey) => ({
        key: `${pageKey}_${componentKey}`,
        label: componentKey,
        extra: Array.isArray(pageData[componentKey]),
        content: pageData[componentKey],
        isComponent: true,
      }));

      return {
        key: pageKey,
        label: pageKey,
        isPage: true,
        components: components,
      };
    });

    return pages;
  }, [filteredContent]);

  const handleImportDOC = () => {
    if (fileInputExportRef.current) fileInputExportRef.current.click();
  };

  const handleFileSelect = async (e) => {
    e.preventDefault();

    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    setIsProcessing(true);

    try {
      const arrayBuffer = await selectedFile.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      if (result?.messages && result?.messages?.length > 0) {
        console.warn("Conversion warnings:", result?.messages);
      }

      const structuredData = parseContent(result.value);
      const content = prePareContent(structuredData, dynamicContentForm);
      setContentJSON((pr) => {
        return deepMerge(pr, content);
      });
    } catch (err) {
      console.error("Error processing file:", err);
    } finally {
      setIsProcessing(false);
      setReset((prev) => !prev);
    }
  };

  return (
    <div className="tw-h-full tw-flex tw-flex-col tw-overflow-hidden">
      {/* Header Section - Fixed */}
      <div className="tw-flex-shrink-0 tw-space-y-3 ">
        <div className="">
          <SearchBar
            disabled={!selectedPage && !selectedComponent}
            type="page"
            handleSearch={(e) => handleSearch(e)}
          />
        </div>

        {/* Selection Status */}
        {/* <div className="tw-flex tw-items-center tw-space-x-2 tw-p-2 tw-bg-blue-50 tw-rounded-lg">
          {selectedPage && (
            <Tag color="blue" className="tw-flex tw-items-center tw-space-x-1">
              <Eye className="tw-w-3 tw-h-3" />
              <span>Page: {selectedPage}</span>
            </Tag>
          )}
          {selectedComponent && (
            <Tag color="green" className="tw-flex tw-items-center tw-space-x-1">
              <Eye className="tw-w-3 tw-h-3" />
              <span>Component: {selectedComponent}</span>
            </Tag>
          )}
          {!selectedPage && (
            <div className="tw-text-gray-500 tw-text-sm tw-flex tw-items-center tw-space-x-1">
              <EyeOff className="tw-w-3 tw-h-3" />
              <span>
                Click on a page or component in the preview to load content
              </span>
            </div>
          )}
        </div> */}

        {/* Debug Info - Remove in production */}
        {/* {process.env.NODE_ENV === "development" && (
          <div className="tw-text-xs tw-text-gray-400 tw-p-2 tw-bg-gray-100 tw-rounded">
            <div>Selected Page: {selectedPage || "None"}</div>
            <div>Selected Component: {selectedComponent || "None"}</div>
            <div>
              Content Keys: {Object.keys(contentJSON).join(", ") || "None"}
            </div>
          </div>
        )} */}

        <div className="tw-flex tw-space-x-4 tw-w-full tw-justify-between tw-items-center">
          <Button
            type="primary"
            size="large"
            onClick={handleImportJSON}
            disabled={
              (!selectedPage && !selectedComponent) || saving || importing
            }
            // disabled={saving || importing}
            className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Import JSON
          </Button>
          {isImportDoc && (
            <Button
              type="primary"
              size="large"
              onClick={handleImportDOC}
              disabled={
                (!selectedPage && !selectedComponent) || saving || isProcessing
              }
              className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Import Doc
            </Button>
          )}
        </div>

        {/* Hidden file inputs */}
        <input
          type="file"
          accept="application/json,.json"
          ref={fileInputRef}
          onChange={onFileChange}
          style={{ display: "none" }}
        />
        <input
          ref={fileInputExportRef}
          type="file"
          accept=".docx"
          onChange={handleFileSelect}
          className="file-input"
          style={{ display: "none" }}
        />
      </div>

      {/* Scrollable Content Section */}
      <div className="tw-flex tw-flex-1 tw-min-h-80 tw-p-4 tw-relative">
        {/* Loading Overlay */}
        {(isAddingItem || isUpdatingContent) && (
          <div
            aria-live="polite"
            aria-busy="true"
            className="tw-absolute tw-inset-0 tw-h-full tw-opacity-75 tw-bg-white tw-flex tw-items-center tw-justify-center tw-z-10 tw-rounded-lg"
          >
            <div className="tw-text-center">
              <Spin size="large" />
              <p className="tw-mt-2 tw-text-gray-600">
                {isAddingItem
                  ? "Adding new position..."
                  : "Updating content..."}
              </p>
            </div>
          </div>
        )}

        <div
          className={`tw-flex-1 tw-overflow-y-auto tw-min-h-80 tw-h-[460px] tw-p-4 tw-relative ${
            isAddingItem || isUpdatingContent || isContentLoading
              ? " tw-pointer-events-none"
              : ""
          }`}
        >
          {/* Content Loading Overlay */}
          {isContentLoading && (
            <div className="tw-absolute tw-inset-0 tw-bg-white tw-bg-opacity-75 tw-flex tw-items-center tw-justify-center tw-z-20 tw-rounded-lg">
              <div className="tw-text-center">
                <Spin size="large" />
                <p className="tw-mt-2 tw-text-gray-600">Loading content...</p>
              </div>
            </div>
          )}

          {itemList?.length ? (
            <div className="tw-space-y-8">
              {itemList?.map((page, pageIndex) => (
                <div key={page.key} className="tw-space-y-4">
                  {/* Page Header */}
                  <div className="tw-flex tw-items-center tw-justify-between tw-space-x-3">
                    <h2 className="tw-text-xl tw-font-bold">
                      {page?.label || ""}
                    </h2>
                  </div>

                  {/* Components within Page */}
                  <div className="tw-space-y-4">
                    {page?.components?.map((component, componentIndex) => (
                      <div key={component?.key} className="tw-space-y-4">
                        {/* Component Header */}
                        <Card className="tw-bg-white tw-rounded-lg tw-border tw-border-gray-200">
                          <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                            <div className="tw-w-full tw-flex tw-items-center tw-justify-between tw-space-x-3">
                              <h3 className="tw-text-lg tw-font-bold tw-text-gray-800">
                                {component?.label}
                              </h3>
                            </div>
                          </div>

                          {/* Component Content */}
                          <div className="tw-space-y-4">
                            {buildItemList(component?.content, [
                              page?.key,
                              component?.label,
                            ])}
                          </div>
                        </Card>

                        {/* Component-level Divider */}
                        {componentIndex < page?.components?.length - 1 && (
                          <Divider className="tw-my-4 tw-border-gray-300" />
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Page-level Divider */}
                  {pageIndex < itemList?.length - 1 && (
                    <Divider className="tw-my-8 tw-border-blue-300 tw-border-2" />
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="tw-text-center tw-py-8">
              <p className="tw-text-gray-500 tw-mb-2">
                {selectedPage
                  ? "No content fields found for this selection"
                  : "Click on a page or component in the preview to load content"}
              </p>
              <p className="tw-text-sm tw-text-gray-400">
                {selectedPage
                  ? "This page/component may not have editable content fields"
                  : "Content fields will appear here when you click on elements in the preview"}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClickBasedContentLoader;
