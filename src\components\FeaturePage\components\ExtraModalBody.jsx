import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Divider,
  Form,
  Input,
  Row,
  Space,
  Tabs,
  Typography,
} from "antd";
import { MinusCircle, Plus } from "lucide-react";
import React from "react";

const { Text } = Typography;

const ExtraModalBody = ({
  form,
  isServiceAreaD = false,
  handleFormChange = () => {},
  isNested = false,
}) => {
  const tabItems = [
    {
      key: "simple",
      label: "Single Items",
      children: (
        <Form
          onValuesChange={handleFormChange}
          layout="vertical"
          size="large"
          form={form}
        >
          <Form.List name="simpleFields">
            {(fields, { add, remove }) => (
              <div className="tw-space-y-4">
                <div className="tw-space-y-3">
                  {fields.map((field, index) => {
                    const isDefaultField =
                      isServiceAreaD == -1
                        ? false
                        : isServiceAreaD
                        ? field.name <= 2 // breadcrumbName, sitemapLabel, state
                        : field.name <= 1; // breadcrumbName, sitemapLabel

                    return (
                      <Row key={field.key} gutter={[8, 8]} align="middle">
                        <Col span={8}>
                          <Form.Item
                            {...field}
                            label={index == 0 ? <Text strong>Key</Text> : ""}
                            name={[field.name, "key"]}
                            className="tw-mb-0"
                            rules={[
                              { required: true, message: "Key is required" },
                            ]}
                          >
                            <Input
                              disabled={isDefaultField}
                              placeholder="Field key"
                            />
                          </Form.Item>
                        </Col>
                        <Col span={14}>
                          <Form.Item
                            {...field}
                            label={index == 0 ? <Text strong>Value</Text> : ""}
                            className="tw-mb-0"
                            name={[field.name, "value"]}
                            rules={[
                              {
                                required: true,
                                message: "Value is required",
                              },
                            ]}
                          >
                            <Input placeholder="Field value" />
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          {!isDefaultField && (
                            <MinusCircle
                              className={`tw-w-5 tw-h-5 tw-text-gray-500 tw-cursor-pointer ${
                                index == 0 ? "tw-mt-7" : ""
                              }`}
                              onClick={() => remove(field?.name)}
                            />
                          )}
                        </Col>
                      </Row>
                    );
                  })}
                </div>
                <Button
                  type="dashed"
                  onClick={() => add({ key: "", value: "" })}
                  block
                  icon={<Plus className="tw-w-4 tw-h-4" />}
                >
                  Add Section Item
                </Button>
              </div>
            )}
          </Form.List>
        </Form>
      ),
    },
    {
      key: "arrays",
      label: "Repeated Items",
      children: (
        <Form
          layout="vertical"
          onValuesChange={handleFormChange}
          size="large"
          form={form}
        >
          <Form.List name="arrayGroups">
            {(groups, { add: addGroup, remove: removeGroup }) => (
              <div className="tw-space-y-4">
                <div className="tw-space-y-4">
                  {groups?.map((groupField) => (
                    <Card key={groupField?.key} size="small">
                      <Space direction="vertical" className="tw-w-full">
                        <Row gutter={[8, 8]} align="middle">
                          <Col span={23}>
                            <Form.Item
                              {...groupField}
                              label={<Text strong>Key</Text>}
                              className="tw-mb-0"
                              name={[groupField.name, "arrayKey"]}
                              rules={[
                                {
                                  required: true,
                                  message: "Array key is required",
                                },
                              ]}
                            >
                              <Input placeholder="Key" />
                            </Form.Item>
                          </Col>
                          <Col span={1}>
                            <MinusCircle
                              className="tw-w-5 tw-h-5 tw-text-gray-500 tw-cursor-pointer tw-mt-7"
                              onClick={() => removeGroup(groupField?.name)}
                            />
                          </Col>
                        </Row>

                        <Divider className="!tw-my-0" />

                        <div className="tw-space-y-2">
                          <div className="tw-flex tw-items-center tw-justify-between">
                            <Text strong className="tw-text-sm tw-font-medium">
                              Items
                            </Text>
                          </div>

                          <Form.List name={[groupField.name, "items"]}>
                            {(items, { add: addItem, remove: removeItem }) => (
                              <div className="tw-space-y-3">
                                {items.map((itemField, itemIndex) =>
                                  !isNested ? (
                                    <Row
                                      key={itemField.key}
                                      gutter={[8, 8]}
                                      align="middle"
                                    >
                                      <Col span={11}>
                                        <Form.Item
                                          {...itemField}
                                          label={
                                            itemIndex === 0 ? (
                                              <Text strong>Key</Text>
                                            ) : (
                                              ""
                                            )
                                          }
                                          className="tw-mb-0"
                                          name={[itemField.name, "key"]}
                                          rules={[
                                            {
                                              required: true,
                                              message: `Key is required`,
                                            },
                                          ]}
                                        >
                                          <Input placeholder={`Enter key`} />
                                        </Form.Item>
                                      </Col>
                                      <Col span={11}>
                                        <Form.Item
                                          {...itemField}
                                          label={
                                            itemIndex === 0 ? (
                                              <Text strong>Value</Text>
                                            ) : (
                                              ""
                                            )
                                          }
                                          name={[itemField.name, "value"]}
                                          className="tw-mb-0"
                                          rules={[
                                            {
                                              required: true,
                                              message: `Value is required`,
                                            },
                                          ]}
                                        >
                                          <Input placeholder={`Enter value`} />
                                        </Form.Item>
                                      </Col>
                                      <Col span={2}>
                                        <MinusCircle
                                          className={`tw-w-5 tw-h-5 tw-text-gray-500 tw-cursor-pointer ${
                                            itemIndex === 0 ? "tw-mt-7" : ""
                                          }`}
                                          onClick={() =>
                                            removeItem(itemField?.name)
                                          }
                                        />
                                      </Col>
                                    </Row>
                                  ) : (
                                    <Card
                                      key={itemField.key}
                                      size="small"
                                      className="tw-bg-gray-50"
                                    >
                                      <div className="tw-flex tw-justify-end">
                                        <MinusCircle
                                          className="tw-w-5 tw-h-5 tw-text-gray-500 tw-cursor-pointer tw-mb-2"
                                          onClick={() =>
                                            removeItem(itemField?.name)
                                          }
                                        />
                                      </div>
                                      <Form.List
                                        name={[itemField.name, "properties"]}
                                      >
                                        {(
                                          properties,
                                          { add: addProp, remove: removeProp }
                                        ) => (
                                          <div className="tw-space-y-2">
                                            {properties.map(
                                              (propField, propIndex) => (
                                                <Row
                                                  key={propField.key}
                                                  gutter={[8, 8]}
                                                  align="middle"
                                                >
                                                  <Col span={11}>
                                                    <Form.Item
                                                      {...propField}
                                                      label={
                                                        propIndex === 0 ? (
                                                          <Text strong>
                                                            Key
                                                          </Text>
                                                        ) : (
                                                          ""
                                                        )
                                                      }
                                                      className="tw-mb-0"
                                                      name={[
                                                        propField.name,
                                                        "key",
                                                      ]}
                                                      rules={[
                                                        {
                                                          required: true,
                                                          message: `Key is required`,
                                                        },
                                                      ]}
                                                    >
                                                      <Input
                                                        placeholder={`Enter key`}
                                                      />
                                                    </Form.Item>
                                                  </Col>
                                                  <Col span={11}>
                                                    <Form.Item
                                                      {...propField}
                                                      label={
                                                        propIndex === 0 ? (
                                                          <Text strong>
                                                            Value
                                                          </Text>
                                                        ) : (
                                                          ""
                                                        )
                                                      }
                                                      name={[
                                                        propField.name,
                                                        "value",
                                                      ]}
                                                      className="tw-mb-0"
                                                      rules={[
                                                        {
                                                          required: true,
                                                          message: `Value is required`,
                                                        },
                                                      ]}
                                                    >
                                                      <Input
                                                        placeholder={`Enter value`}
                                                      />
                                                    </Form.Item>
                                                  </Col>
                                                  <Col span={2}>
                                                    <MinusCircle
                                                      className={`tw-w-5 tw-h-5 tw-text-gray-500 tw-cursor-pointer ${
                                                        propIndex === 0
                                                          ? "tw-mt-7"
                                                          : ""
                                                      }`}
                                                      onClick={() =>
                                                        removeProp(
                                                          propField?.name
                                                        )
                                                      }
                                                    />
                                                  </Col>
                                                </Row>
                                              )
                                            )}
                                            <Button
                                              type="dashed"
                                              onClick={() =>
                                                addProp({
                                                  key: "",
                                                  value: "",
                                                })
                                              }
                                              block
                                              icon={
                                                <Plus className="tw-w-4 tw-h-4" />
                                              }
                                            >
                                              Add Property
                                            </Button>
                                          </div>
                                        )}
                                      </Form.List>
                                    </Card>
                                  )
                                )}
                                <Button
                                  type="dashed"
                                  onClick={() =>
                                    addItem(
                                      isNested
                                        ? {
                                            properties: [
                                              { key: "", value: "" },
                                            ],
                                          }
                                        : { key: "", value: "" }
                                    )
                                  }
                                  block
                                  icon={<Plus className="tw-w-4 tw-h-4" />}
                                >
                                  Add Item
                                </Button>
                              </div>
                            )}
                          </Form.List>
                        </div>
                      </Space>
                    </Card>
                  ))}
                </div>

                <Button
                  type="dashed"
                  onClick={() =>
                    addGroup({
                      arrayKey: "",
                      items: [
                        isNested
                          ? { properties: [{ key: "", value: "" }] }
                          : { key: "", value: "" },
                      ],
                    })
                  }
                  block
                  icon={<Plus className="tw-w-4 tw-h-4" />}
                >
                  Add Repeated Items
                </Button>
              </div>
            )}
          </Form.List>
        </Form>
      ),
    },
  ];
  return (
    <div>
      <Tabs defaultActiveKey="simple" items={tabItems} className="tw-mt-4" />
    </div>
  );
};

export default ExtraModalBody;
