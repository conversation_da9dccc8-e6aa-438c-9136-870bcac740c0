import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>ton, Spin, Divider, Tag } from "antd";
import {
  CheckCircleTwoTone,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import CommonButton from "../../common/CommonButton";

/**
 * ExportModal
 *
 * Props:
 * - open: boolean — control visibility
 * - status: 'idle' | 'loading' | 'success' | 'missing' — current export state
 * - onClose: () => void — close handler
 * - missing?: {
 *     basic?: string[];
 *     branding?: string[];
 *     preview?: string[];
 *     deployment?: string[];
 *   }
 * - onDone?: () => void — called when user presses Done on success
 */
export const ExportModal = ({ open, status, onClose, onDone, missing }) => {
  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={520}
      maskClosable={false}
      closable={false}
      className="tw-bg-transparent"
      styles={{
        body: { padding: 0, background: "transparent" },
        content: { background: "transparent", boxShadow: "none" },
      }}
      bodyStyle={{ padding: 0, background: "transparent" }}
    >
      {/* Card container */}
      <div className="tw-bg-white tw-rounded-3xl tw-shadow-2xl tw-overflow-hidden tw-text-center tw-px-8 tw-pt-10 tw-pb-8">
        {status === "loading" && <LoadingView />}
        {status === "success" && <SuccessView onDone={onDone || onClose} />}
        {status === "missing" && (
          <MissingDataView missing={missing} onAdd={onClose} />
        )}
      </div>
    </Modal>
  );
};

const LoadingView = () => {
  return (
    <div className="tw-flex tw-flex-col tw-items-center tw-gap-5">
      <div className="tw-w-16 tw-h-16 tw-rounded-full  tw-flex tw-items-center tw-justify-center">
        <Spin size="large" />
      </div>
      <h2 className="tw-text-2xl tw-font-semibold tw-text-gray-900">
        Exporting…
      </h2>
      <p className="tw-text-gray-500 tw-max-w-md">
        We’re packaging your project as an HTML bundle. Please keep this window
        open.
      </p>
      {/* <div className="tw-w-full tw-h-2 tw-rounded-full tw-bg-gray-100">
        <div
          className="tw-h-2 tw-rounded-full tw-animate-[pulse_1.2s_ease-in-out_infinite] tw-bg-gradient-to-r tw-from-indigo-500 tw-via-blue-500 tw-to-purple-500"
          style={{ width: "70%" }}
        />
      </div> */}
    </div>
  );
};

const SuccessView = ({ onDone }) => {
  return (
    <div className="tw-flex tw-flex-col tw-items-center tw-gap-5">
      <div className="tw-w-20 tw-h-20 tw-rounded-full tw-bg-green-50 tw-flex tw-items-center tw-justify-center">
        <CheckCircleTwoTone twoToneColor="#22c55e" className="tw-text-4xl" />
      </div>
      <h2 className="tw-text-[28px] tw-font-extrabold tw-text-gray-900">
        Export Successfully
      </h2>
      <p className="tw-text-gray-500">Congratulations! 🎉</p>
      <p className="tw-text-gray-500 tw--mt-2">
        Your project has been exported as an HTML package.
      </p>
      <CommonButton type="primary" text="Done" onClick={onDone} />
    </div>
  );
};

const MissingDataView = ({ missing, onAdd }) => {
  const sections = [
    { title: "Basic Settings", items: missing?.basic || [] },
    { title: "Designing & Branding", items: missing?.branding || [] },
    { title: "Content Preview", items: missing?.preview || [] },
    { title: "Deployment", items: missing?.deployment || [] },
  ];

  return (
    <div className="tw-text-left">
      <div className="tw-flex tw-items-center tw-gap-3 tw-mb-3">
        <div className="tw-w-10 tw-h-10 tw-rounded-full tw-bg-amber-50 tw-flex tw-items-center tw-justify-center">
          <ExclamationCircleOutlined className="tw-text-amber-500 tw-text-xl" />
        </div>
        <h3 className="tw-text-2xl tw-font-semibold tw-text-gray-900">
          Missing Data
        </h3>
      </div>
      <Divider className="tw-my-2" />

      <div className="tw-grid md:tw-grid-cols-2 tw-gap-x-8 tw-gap-y-6">
        {sections?.map((s) => (
          <div key={s?.title}>
            <div className="tw-text-gray-900 tw-font-semibold tw-mb-2">
              {s?.title}
            </div>
            {s?.items?.length ? (
              <ul className="tw-space-y-1">
                {s?.items?.map((it) => (
                  <li
                    key={it}
                    className="tw-flex tw-items-center tw-gap-2 tw-text-gray-600"
                  >
                    <span className="tw-w-1.5 tw-h-1.5 tw-rounded-full tw-bg-gray-400" />
                    {it}
                  </li>
                ))}
              </ul>
            ) : (
              <Tag color="green">All good</Tag>
            )}
          </div>
        ))}
      </div>

      <div className="tw-flex tw-justify-end tw-gap-3 tw-mt-8">
        <Button onClick={onAdd} className="tw-rounded-xl">
          Cancel
        </Button>
        <Button
          type="primary"
          onClick={onAdd}
          className="tw-rounded-xl tw-bg-gradient-to-r tw-from-indigo-500 tw-to-purple-500 tw-border-none"
        >
          Add Data
        </Button>
      </div>
    </div>
  );
};
