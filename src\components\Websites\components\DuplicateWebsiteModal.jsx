import React, { useEffect } from "react";
import { Modal, Form } from "antd";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import FormFields from "../../common/FormFields";
import CommonButton from "../../common/CommonButton";

const DuplicateWebsiteModal = ({ visible, onCancel, website, onSuccess }) => {
  const [form] = Form.useForm();
  const api = useHttp();

  const handleFinish = (values) => {
    const payload = {
      ...values,
      id: website?.id,
    };
    delete payload.name;
    api.sendRequest(
      CONSTANTS.API.websites.copyWebsite,
      (res) => {
        onSuccess({
          ...res?.data,
          websiteversions: res?.data?.versions,
          latestVersion: res?.data?.versions?.[0],
        });
        form.resetFields();
        onCancel();
      },
      payload
    );
  };

  const duplicateWebsiteForm = CONSTANTS.FORM.duplicateWebsite?.map((field) => {
    if (field.name === "versionId") {
      return {
        ...field,
        options: website?.websiteversions?.map((v) => ({
          label: `v${v?.version}`,
          value: v?.id,
        })),
      };
    }
    return field;
  });

  return (
    <Modal
      title="Duplicate Website"
      visible={visible}
      onCancel={onCancel}
      footer={null}
      destroyOnClose
    >
      <Form
        initialValues={{
          versionId: website?.websiteversions?.[0]?.id,
          name: website?.name || "",
        }}
        form={form}
        layout="vertical"
        size="large"
        onFinish={handleFinish}
      >
        {duplicateWebsiteForm?.map((field) => (
          <FormFields key={field.name} field={field} />
        ))}
        <div className="tw-flex tw-justify-end tw-gap-2">
          <CommonButton onClick={onCancel} text="Cancel" type="default" />
          <CommonButton
            htmlType="submit"
            loading={api.isLoading}
            text="Create"
          />
        </div>
      </Form>
    </Modal>
  );
};

export default DuplicateWebsiteModal;
