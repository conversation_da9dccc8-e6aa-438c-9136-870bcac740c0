import { getPagePath } from "../utils/simpleWebsiteExporter";
import { findAndReplaceVariable, getCompanySlug, isNotNullOrEmpty } from "./functions";

export const generatePreviewHTML = (options = {}) => {
    const {
        data = [],
        pageData = {},
        content = {},
        fullData = null,
        isHtml = false,
        // components = [],
        // extraDetail = {},
        // dynamicContentForm,
        // GVariable = {},
        templateObj,
        mediaFiles,
        // bradingDetails,
        isExport = false,
    } = options;
    let bodyContent = "";
    let combinedCSS = "";
    let combinedJS = "";
    // console.log(GVariable, "GVariable");
    // console.log(data, "data");
    if (data?.length) {
        data?.forEach((comp) => {
            comp = { ...comp, ...(comp?.componentversions?.[0] || {}) };
            if (comp) {
                let componentHTML = isHtml ? comp?.html_withValue || "" : comp?.html;
                combinedCSS += comp?.css || "";
                combinedJS += comp?.js || "";
                // Wrap with optional user CSS class

                const cssClass = comp?.class
                    ? ` class="${comp?.class} docx-content"`
                    : "";
                bodyContent += `<div ${comp ? `data-component="${comp?.name}"` : ""
                    }  id="${comp?.id || comp?.componentVersionId
                    }" ${cssClass}>${componentHTML}</div>\n`;
            } else {
                let html = isHtml ? comp?.html_withValue || "" : comp?.html || "";

                bodyContent +=
                    html ||
                    '<p style="color: #6b7280; text-align: center; padding: 2rem;">No content to preview</p>';
                combinedCSS += comp?.css || "";
                combinedJS += comp?.js || "";
            }
        });
    } else {
        if (fullData) {
            bodyContent = fullData || "";
        } else {
            // Empty state for pages
            bodyContent = `
        <div style=" display: flex; align-items: center; justify-content: center; min-height: 100%; text-align: center; color: #6b7280;">
          <div>
            <div style="font-size: 3rem; margin-bottom: 1rem;">+</div>
            <p style="font-size: 1.125rem; margin-bottom: 0.5rem;">Drop components here</p>
            <p style="font-size: 0.875rem;">Drag components from the left panel to build your page</p>
          </div>
        </div>
      `;
        }
    }
    const {
        metaTitle,
        metaDescription,
        customCss,
        customJs,
        wrapperClass,
        urlSlug,
        url,
        slug,
        headers,
    } = pageData;
    // const { default_city, default_state, company_name, website_url } = GVariable;
    // const comapnyNameSplit = company_name?.split(" ")?.slice(0, 3)?.join(" ");
    // const servieAreaCompanyName = `${comapnyNameSplit} ${pageData?.name}`;
    // const pageLogoName =
    //     pageData?.oldName == serviceAreaDKey ? servieAreaCompanyName : company_name;
    // const footerExtra = dynamicContentForm?.[
    //     serviceAreaDKey
    // ]?.sections?.[0]?.sectionItems?.find(
    //     (item) => item?.label == pageData?.name
    // )?.extra;
    // const dState =
    //     pageData?.oldName == serviceAreaDKey ? footerExtra?.state : default_state;
    // const dCity =
    //     pageData?.oldName == serviceAreaDKey ? pageData?.name : default_city;
    // const contentKeys = {
    //     ...GVariable,
    //     slug_key: slug?.slug,
    //     slug: pageData?.urlSlug,
    //     oldSlug: pageData?.oldSlug,
    //     oldName: pageData?.oldName,
    //     company_name: pageLogoName,
    //     company_name_small: getCompanySlug(pageLogoName),
    //     default_city: dCity,
    //     default_state: dState,
    //     default_state_small: getCompanySlug(dState),
    //     default_city_small: getCompanySlug(dCity),
    //     staticBreadcrumbName:
    //         pageData?.oldSlug == "buses"
    //             ? dynamicContentForm?.[busesDKey]?.breadcrumbName
    //             : pageData?.oldSlug == "service-area"
    //                 ? dynamicContentForm?.[serviceAreaDKey]?.breadcrumbName
    //                 : pageData?.oldSlug == "services"
    //                     ? dynamicContentForm?.[serviceDKey]?.breadcrumbName
    //                     : dynamicContentForm?.[pageData?.oldName]?.breadcrumbName || "",
    //     ...dynamicContentForm?.[pageData?.oldName]?.sections
    //         ?.flatMap((section) => section?.sectionItems || [])
    //         ?.find((item) => item?.label === pageData?.name)?.extra,
    // };
    // console.log(contentKeys, "contentKeys", dynamicContentForm, pageData);
    const scriptTag = headers || "";
    const scriptRes = findAndReplaceVariable({
        str: scriptTag,
        content: content,
        mediaFiles,
        isExport,
    });
    // console.log(scriptRes, "scriptRes...");
    let scriptHtml = scriptRes?.str
        ? JSON.parse(JSON.stringify(scriptRes?.str))
        : "";
    // console.log(scriptHtml, "jgdjfbvjdf");
    const wrapperClasses = isNotNullOrEmpty(wrapperClass)
        ? ` class="${wrapperClass}"`
        : "";


    //    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    // console.log(scehma, "scehma", servicesScehma, pageData);
    combinedCSS += isNotNullOrEmpty(customCss) ? ` ${customCss}` || "" : "";
    combinedJS += isNotNullOrEmpty(customJs) ? ` ${customJs}` || "" : "";

    // Add click detection script for iframe communication
    const pageName = pageData?.name || "";
    const clickDetectionScript = !isExport
        ? `
    <script>
      (function() {
        try {
          // Prevent default link behavior and scrolling
          document.addEventListener('click', function(e) {
            try {
              // Prevent default behavior for all links
              if (e.target.tagName === 'A' || e.target.closest('a')) {
                e.preventDefault();
                e.stopPropagation();
              }
              
              // // Find clicked component
              // const component = e.target.closest('[data-component]');
              // if (component) {
              //   const componentName = component.getAttribute('data-component');
              //   const componentId = component.getAttribute('id');
               // Get all ancestors with [data-component]
          const components = e.composedPath()
            .filter(el => el instanceof HTMLElement && el.hasAttribute && el.hasAttribute('data-component'));

          if (components.length > 0) {
            // Instead of closest, take the outermost (last in the path)
            const component = components[components.length - 1];
            const componentName = component.getAttribute('data-component');
            const componentId = component.getAttribute('id');
                
                // Send component click message to parent
                window.parent.postMessage({
                  type: 'COMPONENT_CLICK',
                  data: {
                    pageName: '${pageName}',
                    componentName: componentName,
                    componentId: componentId,
                    position: { x: e.clientX, y: e.clientY }
                  }
                }, '*');
                return;
              }
              
              // Find clicked page wrapper
              const pageWrapper = e.target.closest('[data-page]');
              if (pageWrapper) {
                const pageName = pageWrapper.getAttribute('data-page');
                
                // Send page click message to parent
                window.parent.postMessage({
                  type: 'PAGE_CLICK',
                  data: {
                    pageName: pageName,
                    position: { x: e.clientX, y: e.clientY }
                  }
                }, '*');
              }
            } catch(clickError) {
              console.error('Click handler error:', clickError);
            }
          });
          
          // Prevent scrolling
          // document.addEventListener('wheel', function(e) {
          //   try {
          //     e.preventDefault();
          //   } catch(wheelError) {
          //     console.error('Wheel handler error:', wheelError);
          //   }
          // }, { passive: false });
          
          // // Prevent touch scrolling
          // document.addEventListener('touchmove', function(e) {
          //   try {
          //     e.preventDefault();
          //   } catch(touchError) {
          //     console.error('Touch handler error:', touchError);
          //   }
          // }, { passive: false });
          
          // Add visual indicators for clickable elements
          const style = document.createElement('style');
          style.textContent = '[data-component] { cursor: pointer !important; position: relative; } [data-component]:hover { outline: 2px solid #3b82f6 !important; outline-offset: 2px !important; } [data-page] { cursor: pointer !important; } [data-page]:hover { outline: 2px solid #10b981 !important; outline-offset: 2px !important; }';
          document.head.appendChild(style);
        } catch(e) {
          console.error('JavaScript error:', e);
        }
      })();
    </script>
  `
        : "";

    // combinedJS += clickDetectionScript;

    bodyContent = wrapperClasses
        ? `<div data-page="${pageData?.name}"  ${wrapperClasses}>${bodyContent}</div>\n`
        : bodyContent;

    // const currentURL = `${website_url}${getPagePath(pageData)}`;


    const metaRes = findAndReplaceVariable({
        str: "",
        content: content,
        // {
        //     ...GVariable,
        //     slug_key: slug?.slug,
        //     slug: pageData?.oldName == serviceAreaDKey ? pageData?.name : slug?.label,
        //     // slug: slug?.label,
        //     default_city_small: getCompanySlug(default_city),
        //     default_state:
        //         pageData?.oldName == serviceAreaDKey
        //             ? footerExtra?.state
        //             : default_state,
        //     // default_city: pageData?.oldName == serviceAreaDKey ? pageData?.name : default_city,
        //     company_name: pageLogoName,
        //     // state: footerExtra?.state || GVariable?.default_state || "",
        // },
        mediaFiles,
        isExport,
    });

    const tailwindConfig = `{
    content: ["./*.html"],
    prefix: "dw-",
    theme: {
      extend: {
        colors: {
          body: {
            bg: "${templateObj?.backgroundColor || "#ffffff"
        }", // --body-bg-color
            text: "${templateObj?.textColor || "#333333"
        }", // --body-text-color
          },
          theme: {
            main: "${templateObj?.primaryColor || "#d5232b"
        }", // --theme-main-color
            mainColorShade: "${templateObj?.secondaryColor || "#C41421"
        }", // "#C41421",
            hoverBox: "rgba(82, 164, 206, 0.43)", // --theme-box-hover-color
            cardBorder: "${templateObj?.borderColor || "#e0e0e0"}",
            cardBg: "${templateObj?.cardBackgroundColor || "#fafafa"}",
            divider: "${templateObj?.dividerColor || "#eee"}",
            lowOpacityTextColor: "#111827",
          },
        },
        fontFamily: {
          heading: ["Phudu", "sans-serif"], // --theme-heading-font
          text: ["Inter", "sans-serif"], // --theme-text-font
        },
        screens: {
          xs: "200px",
          smx: "376px",
          smm: "567px",
          mdx: "768px",
          mdl: "993px",
          lgx: "1171px",
          lgs: "1221px",
          lgm: "1281px",
          lgl: "1361px",
          xlx: "1400px",
          xl1: "1441px",
          xl2: "1600px",
          xxl: "1900px",
        },
      },
    },
    plugins: [],
  }`;
    // ${generateTailwindCSS()}
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
${metaRes?.str || ""}
  <!-- Tailwind CSS - Generated locally to avoid CDN warnings -->
<style>
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Phudu;
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Phudu;
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
          .pop {
        cursor: pointer;
        }
        /* Lists */
.docx-content ul {
  list-style-type: disc;
  margin: 0.5rem 0 0.5rem 1.5rem;
  padding: 0;
  padding-left: 1.5rem;
}

.docx-content ol {
  list-style-type: decimal;
  margin: 0.5rem 0 0.5rem 1.5rem;
  padding: 0;
  padding-left: 1.5rem;
}

.docx-content li {
  margin-bottom: 0.4rem;
}
    </style>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs" defer></script>
    <script>
      tailwind.config = ${tailwindConfig};
    </script>
  

  <style>
    /* Custom component styles */
    ${combinedCSS}
  </style>
<style>
p a {
  color: ${templateObj?.primaryColor || "#d5232b"};
}
</style>
</head>
<body class="dw-bg-body-bg dw-text-body-text dw-font-text">
  ${bodyContent}
  ${scriptHtml}
  <script>
    try {
      ${combinedJS}
    } catch(e) {
      console.error('JavaScript error:', e);
    }
  </script>
  ${clickDetectionScript}
</body>
</html>`;
};