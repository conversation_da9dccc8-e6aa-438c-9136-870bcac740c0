import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Button,
  Space,
  Spin,
  List,
} from "antd";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { statCards } from "../../util/content";
import Activitytable from "../ActivityLogs/Activitytable";

const { Text } = Typography;

const Dashboard = () => {
  const api = useHttp();
  const [stats, setStats] = useState({
    websites: 0,
    components: 0,
    pages: 0,
    templates: 0,
  });
  const [activityLogs, setActivityLogs] = useState([]);

  // const [recentActivity, setRecentActivity] = useState([]);

  useEffect(() => {
    api.sendRequest(CONSTANTS.API.dashboard.get, (res) => {
      setStats(res || {});
    });
  }, []);

  if (api.isLoading) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-h-96">
        <Spin size="large" tip="Loading dashboard data..." />
      </div>
    );
  }

  return (
    <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Welcome Header */}
        {/* <div className="tw-mb-8">
          <Title level={2} className="!tw-mb-2">
            Welcome back, {user?.username || "User"}! 👋
          </Title>
          <Text type="secondary" className="tw-text-lg">
            Here's what's happening with your website builder today.
          </Text>
          <Divider className="tw-my-6" />
        </div> */}

        {/* Stats Grid */}
        <Row gutter={[24, 24]} className="tw-mb-8">
          {statCards?.map((stat) => (
            <Col xs={24} sm={12} lg={6} key={stat.name}>
              <Card
                className="tw-h-full tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-0 tw-rounded-2xl"
                styles={{
                  body: {
                    padding: "24px",
                  },
                }}
              >
                <div className="tw-flex tw-items-start tw-space-x-4">
                  <div className={`tw-p-4 tw-rounded-xl ${stat.bgColor}`}>
                    <stat.icon className={`tw-w-6 tw-h-6 ${stat.iconColor}`} />
                  </div>
                  <div>
                    <Statistic
                      title={
                        <Text className="tw-text-sm tw-font-medium tw-text-gray-600">
                          {stat?.name}
                        </Text>
                      }
                      value={stats?.[stat?.key] || 0}
                      valueStyle={{
                        color: "#1f2937",
                        fontSize: "2rem",
                        fontWeight: "bold",
                      }}
                    />
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
        <Activitytable
          activityLogs={activityLogs}
          setActivityLogs={setActivityLogs}
          isSearch={false}
          isPagination={false}
        />
        {/* <LatestActivityLogs limit={10} compact={true} /> */}

        {/* Additional Stats Section */}
        {/* <Row gutter={[24, 24]} className="tw-mt-8">
          <Col xs={24}>
            <Card
              title={
                <Space>
                  <Activity className="tw-w-5 tw-h-5 tw-text-purple-600" />
                  <span className="tw-text-lg tw-font-semibold">
                    Recent Activity
                  </span>
                </Space>
              }
              className="tw-shadow-sm hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-0 tw-rounded-2xl"
              styles={{
                body: {
                  padding: "24px",
                },
              }}
            >
              <div className="tw-text-center tw-py-8">
                <ClockCircleOutlined className="tw-text-4xl tw-text-gray-400 tw-mb-4" />
                <Text type="secondary" className="tw-text-lg">
                  No recent activity yet. Start building to see your progress
                  here!
                </Text>
              </div>
            </Card>
          </Col>
        </Row> */}
      </div>
    </div>
  );
};

// Helper function to get step colors
const getStepColor = (color) => {
  const colorMap = {
    blue: "#3B82F6",
    purple: "#8B5CF6",
    green: "#10B981",
    orange: "#F59E0B",
    red: "#EF4444",
  };
  return colorMap[color] || "#6B7280";
};

export default Dashboard;
