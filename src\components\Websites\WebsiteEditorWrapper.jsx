import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { Spin } from "antd";
import useHttp from "../../hooks/use-http";
import { apiGenerator, imageObj } from "../../util/functions";
import WebsiteEditor from "./WebsiteEditor";

const WebsiteEditorWrapper = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  // const api = useStorage();
  const api = useHttp();
  const [websiteData, setWebsiteData] = useState(null);
  const [templateData, setTemplateData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refresh, setRefresh] = useState(false);
  const [globalvariables, setGlobalvariables] = useState(null);
  useEffect(() => {
    // Fetch pages on component mount
    api.sendRequest(
      CONSTANTS.API.templates.getForTemplate,
      (res) => {
        console.log("Templates fetched for website editor:", res);
        setTemplateData(res?.data);
      },
      null,
      null,
      (error) => {
        console.error("Error fetching pages:", error);
      }
    );
    api.sendRequest(
      CONSTANTS.API.globalVarible.get,
      (response) => {
        const variablesString = response?.data?.variables || "";
        setGlobalvariables(variablesString);
      },
      null,
      null,
      (error) => {
        console.error("Error loading global variables:", error);
        message.error("Failed to load global variables");
      }
    );
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch pages first
        // api.sendRequest(CONSTANTS.API.pages.get, (res) => {
        //   console.log("Pages fetched for template editor:", res);
        //   setPages(res);
        // });

        // If editing existing template, fetch template data
        if (id && id !== "add") {
          setLoading(true);
          // Use direct operation instead of sendRequest for getById
          api.sendRequest(
            apiGenerator(CONSTANTS.API.websites.getById, { id }),
            (res) => {
              const websiteData = res?.data;
              const websiteVersionData = websiteData?.latestVersion;
              setWebsiteData({
                ...websiteData,
                ...websiteVersionData,
                websiteId: websiteData?.id,
                websiteVersionId: websiteVersionData?.id,
                websiteVersion: websiteVersionData?.version,
                websiteContentId: websiteVersionData?.contentId,
                content: websiteVersionData?.content?.content,
                websiteJSON: websiteVersionData?.content?.content,
                templateVersionId: websiteVersionData?.templateversionId,
                templateMediaSet: {
                  ...(websiteVersionData?.templateversion?.mediaSet?.medias
                    ? imageObj(
                        websiteVersionData?.templateversion?.mediaSet?.medias ||
                          {}
                      )
                    : {}),
                },
                mediaSet: {
                  ...(websiteVersionData?.mediaSet?.medias
                    ? imageObj(websiteVersionData?.mediaSet?.medias || {})
                    : {}),
                },
              });
              console.log("Template fetched for editing:", res);
              setLoading(false);
            }
          );
        } else {
          // Creating new template
          setWebsiteData(null);
          setLoading(false);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [id, refresh]);

  const handleCancel = () => {
    // Navigate back to websites list
    navigate("/websites");
  };

  if (loading) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-h-screen tw-w-full">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <WebsiteEditor
      setRefresh={setRefresh}
      websiteData={websiteData}
      templateData={templateData}
      onCancel={handleCancel}
      globalvariables={globalvariables}
    />
  );
};

export default WebsiteEditorWrapper;
