import React, { useState } from "react";
import {
  Card,
  ColorPicker,
  Tooltip,
  Upload,
  Typography,
  Row,
  Col,
  Modal,
  Image,
} from "antd";
import { Loader, Plus } from "lucide-react";

const { Text } = Typography;

const SwatchButton = ({ color, active, onClick, value }) => (
  <Tooltip key={color} title={`Select ${color}`}>
    <div
      className={`tw-w-10 tw-h-10 tw-p-[3px] tw-border-solid tw-border-2 tw-rounded-full tw-transition-all`}
      style={{
        borderColor: active ? color : "#fff",
        boxShadow: active ? `0 0 0 2px ${color}33` : "none",
      }}
    >
      <button
        type="button"
        onClick={() => onClick(color)}
        className="tw-w-full tw-h-full tw-rounded-full tw-transition-all tw-cursor-pointer hover:tw-scale-110 tw-border-0 tw-shadow-sm hover:tw-shadow-md"
        // className={`tw-w-10 tw-h-10 tw-rounded-full tw-border-2 tw-transition-all tw-cursor-pointer hover:tw-scale-110 ${"tw-border-gray-300 hover:tw-border-gray-400"}`}
        style={{ backgroundColor: color }}
      />
    </div>
  </Tooltip>
);

const ColorRow = ({ label, value, onPick, swatches = [] }) => (
  <div className="tw-mb-4 tw-space-y-3">
    <div className="tw-flex tw-items-center tw-justify-between ">
      <Text className="tw-text-sm tw-font-medium tw-text-font-color">
        {label}
      </Text>
      <span className="tw-text-xs tw-text-gray-400">
        {value?.toUpperCase?.() || ""}
      </span>
    </div>
    <div className="tw-flex  tw-gap-2 ">
      {swatches?.map((c) => (
        <SwatchButton
          key={c}
          color={c}
          active={value === c}
          onClick={onPick}
          value={value}
        />
      ))}
    </div>
    <div>
      <ColorPicker
        size="large"
        format="hex"
        value={value}
        onChange={(c) => onPick(c.toHexString())}
        className="custom-color-picker tw-w-full"
      />
    </div>
    {/* <div
      className="tw-h-10 tw-rounded-md tw-border tw-border-gray-300"
      style={{ backgroundColor: value }}
    /> */}
  </div>
);

const UploadCard = ({ label, files = [], onChange }) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");

  const handleCancel = () => setPreviewOpen(false);

  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  };

  const handleChange = async ({ file, fileList }) => {
    if (file.status === "removed") {
      onChange([]);
      return;
    }

    const newFileList = fileList.slice(-1);

    if (newFileList[0] && !newFileList[0].url && !newFileList[0].preview) {
      newFileList[0].preview = await getBase64(newFileList[0].originFileObj);
    }

    onChange(newFileList);
  };

  return (
    <div>
      <Text className="tw-text-sm tw-font-medium tw-text-font-color tw-block tw-mb-2">
        {label}
      </Text>
      <Upload
        accept="image/*"
        listType="picture-card"
        fileList={files}
        beforeUpload={() => false}
        onChange={handleChange}
        onPreview={handlePreview}
        className="avatar-uploader"
      >
        {files.length < 1 && (
          <button
            className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-h-full"
            style={{ border: 0, background: "none" }}
            type="button"
          >
            <Plus />
            <div style={{ marginTop: 8 }}>Upload</div>
          </button>
        )}
      </Upload>
      {previewImage && (
        <Image
          wrapperStyle={{ display: "none" }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(""),
          }}
          src={previewImage}
        />
      )}
    </div>
  );
};

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

export default function BrandingDetailsCard({
  values,
  onChange,
  predefinedColors = [],
}) {
  const neutrals = [
    "#000000",
    "#111111",
    "#1f2937",
    "#374151",
    "#4b5563",
    "#6b7280",
    "#9ca3af",
    "#d1d5db",
    "#e5e7eb",
    // "#f3f4f6",
  ];
  const lights = ["#ffffff", "#f9fafb", "#f3f4f6", "#f5f5f5", "#f8fafc"];
  const borders = ["#d9d9d9", "#e5e7eb", "#e2e8f0", "#cbd5e1", "#94a3b8"];
  const cardBackgrounds = [
    "#ffffff",
    "#f9fafb",
    "#f3f4f6",
    "#f5f5f5",
    "#f8fafc",
  ];
  const dividers = ["#e5e7eb", "#e2e8f0", "#cbd5e1", "#94a3b8", "#6b7280"];

  const brandColorsFields = [
    {
      label: "Primary Color",
      swatches: predefinedColors,
      value: values.primaryColor,
      onChange: (v) => onChange({ primaryColor: v }),
    },
    {
      label: "Secondary Color",
      swatches: predefinedColors,
      value: values.secondaryColor,
      onChange: (v) => onChange({ secondaryColor: v }),
    },
    {
      label: "Background Color",
      swatches: lights,
      value: values.backgroundColor,
      onChange: (v) => onChange({ backgroundColor: v }),
    },
    {
      label: "Text Color",
      swatches: neutrals,
      value: values.textColor,
      onChange: (v) => onChange({ textColor: v }),
    },
    {
      label: "Border Color",
      swatches: borders,
      value: values.borderColor,
      onChange: (v) => onChange({ borderColor: v }),
    },
    {
      label: "Card Background Color",
      swatches: cardBackgrounds,
      value: values.cardBackgroundColor,
      onChange: (v) => onChange({ cardBackgroundColor: v }),
    },
    {
      label: "Divider Color",
      swatches: dividers,
      value: values.dividerColor,
      onChange: (v) => onChange({ dividerColor: v }),
    },
  ];

  return (
    // <Card className="">
    <>
      {/* <div className="tw-text-base tw-font-semibold tw-mb-4">
        Branding Details
      </div> */}
      {/* <div className="tw-grid md:tw-grid-cols-2 tw-gap-6"> */}
      <Row gutter={[16, 16]}>
        {brandColorsFields?.map((field) => (
          <Col xs={24} sm={12} key={field.label}>
            <ColorRow
              label={field.label}
              value={field.value}
              onPick={field.onChange}
              swatches={field.swatches}
            />
          </Col>
        ))}
        {/* <Col xs={24} sm={12}>
          <UploadCard
            label="Favicon"
            files={values?.faviconFiles}
            key={"faviconFiles"}
            onChange={(list) => onChange({ faviconFiles: list })}
          />
        </Col> */}
      </Row>
      {/* <div>
        <div className="tw-grid tw-grid-cols-2 tw-gap-6 tw-mt-4"> */}
      {/* <UploadCard
            label="Logo"
            key={"logoFiles"}
            files={values?.logoFiles}
            onChange={(list) => onChange({ logoFiles: list })}
          /> */}
      {/* <UploadCard
            label="Favicon"
            files={values?.faviconFiles}
            key={"faviconFiles"}
            onChange={(list) => onChange({ faviconFiles: list })}
          />
        </div>
      </div> */}
      {/* </div> */}
    </>
  );
}
