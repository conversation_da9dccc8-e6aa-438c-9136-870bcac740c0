import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Card, Form, Row, Col, Space } from "antd";
import DynamicTab from "../../FeaturePage/components/DynamicTab";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import FormFields from "../../common/FormFields";
import CommonButton from "../../common/CommonButton";

const BasicSettingTab = ({
  website,
  templateData,
  setFormData,
  formData,
  handleSubmit,
  onCancel,
  setdynamicForms,
  dynamicForms,
  templateGetByIdHander,
  selectedTemplate,
  setSelectedTemplate,
  loading = false,
  versionLoading = false,
}) => {
  const [form] = Form.useForm();
  useEffect(() => {
    if (formData) {
      console.log("formData", formData);
      form.setFieldsValue(formData);
    }
  }, [website, form, formData]);
  console.log(form?.getFieldsValue(), "form", selectedTemplate, formData);
  useEffect(() => {
    if (!website) return;
    if (selectedTemplate && website.templateId == selectedTemplate.id) {
      return;
    }
    if (templateData?.length) {
      const templateFind = templateData?.find(
        (t) => t?.id == website?.templateId
      );

      templateGetByIdHander(website?.templateversionId, {
        id: website?.templateId,
        hasDynamicPages: true,
        ...templateFind,
      });
      // form.setFieldsValue({
      //   templateVersionId: website?.templateversionId,
      // });
    }
  }, [website?.templateversionId]);

  const handleTemplateChange = useCallback(
    (value, option) => {
      // setSelectedTemplate({ id: value, hasDynamicPages: true, ...option?.extra, templateId: value });
      const templateVersion = option?.extra?.templateversions?.sort(
        (a, b) => b?.version - a?.version
      )?.[0];
      templateGetByIdHander(templateVersion?.id, {
        id: value,
        hasDynamicPages: true,
        ...option?.extra,
        templateId: value,
      });
      form.setFieldsValue({
        templateVersionId: templateVersion?.id,
      });
      setFormData((pr) => ({
        ...pr,
        templateId: value,
        templateVersionId: templateVersion?.id,
      }));
    },
    [templateGetByIdHander, form, setFormData]
  );
  const dynamicWebsiteInfoFields = useMemo(() => {
    console.log("dynamicWebsiteInfoFields...");
    return CONSTANTS.FORM.websiteInformation?.map((field) => {
      if (field.name === "templateId") {
        return {
          ...field,
          type: "select",
          showSearch: true,
          options: templateData?.map((td) => ({
            label: td.name,
            value: td.id,
            extra: td,
          })),
          onChange: handleTemplateChange,
          labelInValue: false,
        };
      }
      if (field?.name === "templateVersionId") {
        return {
          ...field,
          type: "select",
          options: selectedTemplate?.templateversions?.map((v) => ({
            label: `v${v?.version}`,
            value: v?.id,
          })),
          loading: versionLoading,
          disabled: !selectedTemplate || versionLoading,
          onChange: (value, option) => {
            templateGetByIdHander(value);
          },
          required: !!selectedTemplate,
          rules: !!selectedTemplate
            ? [
                {
                  required: true,
                  message: "Please select a template version",
                },
              ]
            : [],
        };
      }
      return field;
    });
  }, [
    selectedTemplate,
    templateData,
    versionLoading,
    handleTemplateChange,
    templateGetByIdHander,
  ]);

  return (
    <div className=" tw-space-y-6">
      <Card
        className=" tw-border tw-border-[#D9DBDF] tw-rounded-2xl"
        styles={{
          header: {
            borderBottom: "1px solid #f0f0f0",
            paddingBottom: "0px",
          },
          body: {
            paddingTop: "0px",
          },
        }}
      >
        <Space className="tw-mt-4">
          <span className="tw-text-lg tw-font-semibold tw-text-gray-900">
            Website Information
          </span>
        </Space>
        <Form
          form={form}
          initialValues={formData}
          className="tw-mt-6"
          layout="vertical"
          size="large"
          onChange={(e, allValues) => {
            const formValues = form.getFieldsValue();
            // console.log(formValues, "formValues");
            setFormData((pr) => ({
              ...pr,
              ...formValues,
            }));
          }}
        >
          <Row gutter={16}>
            {dynamicWebsiteInfoFields?.map((field) => (
              <Col xs={24} sm={field?.width || 24} key={field.name}>
                <FormFields
                  field={{
                    ...field,
                    ...(field.name === "template" && {
                      onChange: handleTemplateChange,
                    }),
                  }}
                />
              </Col>
            ))}
          </Row>
        </Form>
      </Card>

      {selectedTemplate && selectedTemplate.hasDynamicPages && (
        <DynamicTab
          templatePage={selectedTemplate}
          pages={formData?.websiteDynamicPageData}
          setPages={setFormData}
        />
      )}

      <div className="tw-flex tw-items-center tw-justify-end tw-space-x-2">
        <CommonButton onClick={onCancel} type="text" text="Cancel" />
        <CommonButton
          onClick={() => handleSubmit(formData)}
          loading={loading}
          text={"Save Website"}
        />
      </div>
    </div>
  );
};

export default BasicSettingTab;
