import { Button } from "antd";
import React from "react";

const CommonButton = (props) => {
  let customClass = "";
  if (props?.type === "text") {
    customClass =
      "tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors";
  } else if (props?.type === "default") {
    customClass =
      "tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-border-gray-300 hover:tw-border-gray-400";
  } else {
    customClass =
      "tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0";
  }
  return (
    <Button
      type="primary"
      size="large"
      // onClick={() => {
      //   navigate("/components/add");
      //   setShowEditor(true);
      // }}
      //   icon={<Plus className="tw-w-4 tw-h-4 tw-mr-2" />}
      {...props}
      className={`${props?.className ?? ""} ${customClass}`}
    >
      {props?.text}
    </Button>
  );
};

export default CommonButton;
