import React from "react";
import { Card, Collapse, Typography } from "antd";
import BrandingDetailsCard from "../../FeaturePage/components/BrandingDetailsCard";
import ContentTab from "../../Templates/Component/ContentTab";
import { predefinedColors } from "../../../util/content";
import { ChevronDown } from "lucide-react";
import WebsiteContentTab from "./websiteContentTab";

const { Text } = Typography;

const BrandingContentTab = ({
  formData,
  setFormData,
  onCancel,
  handleSubmit,
  loading,
  pageList,
  setPageList,
  reset,
  setReset,
  selectPage,
  setSelectPage,
  templateData,
  selectedTemplate,
}) => {
  const handleBrandingChange = (patch) => {
    setFormData((prev) => ({ ...prev, ...patch }));
  };
  //   console.log(templateData, formData);
  return (
    <div className="tw-space-y-4">
      <Collapse
        size="large"
        className="tw-bg-white tw-rounded-2xl"
        bordered={false}
        expandIcon={({ isActive }) => (
          <ChevronDown
            className={`tw-w-4 tw-h-4 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
              isActive ? "tw-rotate-180" : ""
            }`}
          />
        )}
        expandIconPosition="end"
        items={[
          {
            key: "branding_details",
            label: (
              //   <span className="tw-text-lg tw-font-semibold  tw-text-gray-600">
              <Text className="tw-text-lg" strong>
                Branding Details
              </Text>
              //   </span>
            ),
            // style: { background: "none", border: "none", borderRadius: "20px" },
            children: (
              <BrandingDetailsCard
                values={formData}
                onChange={handleBrandingChange}
                predefinedColors={predefinedColors}
              />
            ),
          },
        ]}
      />
      {/* <BrandingDetailsCard
        values={formData}
        onChange={handleBrandingChange}
        predefinedColors={predefinedColors}
      /> */}
      {/* <Card className="mt-6 tw-rounded-3xl tw-border tw-text-gray-600"> */}
      <WebsiteContentTab
        formData={formData}
        setFormData={setFormData}
        onCancel={onCancel}
        handleSubmit={handleSubmit}
        loading={loading}
        pageList={pageList}
        setPageList={setPageList}
        templateData={templateData}
        selectedTemplate={selectedTemplate}
        reset={reset}
        setReset={setReset}
        selectPage={selectPage}
        setSelectPage={setSelectPage}
        dynamicpages={formData?.websiteDynamicPageData}
        isWebsite={true}
      />
      {/* </Card> */}
    </div>
  );
};

export default BrandingContentTab;
