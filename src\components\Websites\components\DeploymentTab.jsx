import React, { useEffect, useState } from "react";
import {
  Card,
  Form,
  Row,
  Col,
  Button,
  List,
  Space,
  Typography,
  Tag,
  Empty,
  message,
} from "antd";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import FormFields from "../../common/FormFields";
import CommonButton from "../../common/CommonButton";
import { Download } from "lucide-react";
import { Link } from "react-router-dom";
import useHttp from "../../../hooks/use-http";
import { apiGenerator } from "../../../util/functions";
import { ExportModal } from "./ExportModal";
import ScrollPagination from "../../common/ScrollPagination";

const { Text } = Typography;

const DeploymentTab = ({
  website,
  formData,
  setFormData,
  templateData,
  loading,
  onCancel,
  handleSubmit,
}) => {
  const [form] = Form.useForm();
  const api = useHttp();
  const apiExport = useHttp();
  const apiDeploy = useHttp();
  const [isExport, setIsExport] = useState(false);
  const [missing, setMissing] = useState({});
  const [status, setStatus] = useState("loading");
  const [refreshKey, setRefreshKey] = useState(1);

  useEffect(() => {
    // if (formData?.websiteId) {
    setRefreshKey((prev) => prev + 1);
    // }
  }, [formData?.websiteId]);

  const loadHistory = async ({ page, pageSize }) => {
    return new Promise(async (resolve, reject) => {
      try {
        if (formData?.websiteId) {
          api.sendRequest(
            apiGenerator(
              CONSTANTS.API.deploymentHistory.get,
              {},
              `?websiteId=${formData?.websiteId}&page=${page}&limit=${pageSize}`
            ),
            (res) => {
              resolve({
                items: res?.data?.rows,
                totalCount: res?.data?.count,
              });
            },
            null,
            null,
            (error) => {
              console.error("Error fetching deployment history:", error);
              reject(error);
            }
          );
        } else {
          resolve({ items: [], totalCount: 0 });
        }
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleFormChange = (changedValues, allValues) => {
    setFormData((prev) => ({
      ...prev,
      ...allValues,
    }));
  };

  const exportHandler = (item) => {
    if (!formData?.websiteId) return;
    const payload = {};
    if (item?.websiteId) {
      payload.websiteId = item?.websiteId;
    }
    if (item?.websiteversionId) {
      payload.websiteversionId = item?.websiteversionId;
    }
    setIsExport(true);
    setStatus("loading");
    apiExport.sendRequest(
      apiGenerator(CONSTANTS.API.deployement.exportWebsite, {
        id: website?.id,
      }),
      (res) => {
        setStatus("success");
        console.log("Exported website:", res);
      },
      payload,
      null,
      (error) => {
        console.error("Export failed:", error);
      }
    );
  };

  const triggerDeploy = (item) => {
    const payload = {};
    if (item?.websiteId) {
      payload.websiteId = item?.websiteId;
    }
    if (item?.websiteversionId) {
      payload.websiteversionId = item?.websiteversionId;
    }
    apiDeploy.sendRequest(
      apiGenerator(CONSTANTS.API.deployement.deploy),
      (res) => {
        setStatus("success");
        console.log("Triggered deploy:", res);
      },
      payload,
      null,
      (error) => {
        console.error("Trigger deploy failed:", error);
      }
    );
  };

  // const historyData = [
  // { version: "1.0.0", status: "Success", date: "Jul 5 at 7:30 PM" },
  // { version: "1.0.1", status: "Failed", date: "Jul 6 at 8:00 PM" },
  // { version: "1.0.1", status: "Failed", date: "Jul 6 at 8:00 PM" },
  // { version: "1.0.1", status: "Failed", date: "Jul 6 at 8:00 PM" },
  // { version: "1.0.1", status: "Failed", date: "Jul 6 at 8:00 PM" },
  // ];

  return (
    <div className="tw-space-y-6">
      <Card
        className=" tw-border tw-border-solid tw-border-[#D9DBDF] tw-rounded-2xl"
        styles={{
          header: {
            borderBottom: "1px solid #f0f0f0",
            paddingBottom: "0px",
          },
          body: {
            paddingTop: "0px",
          },
        }}
      >
        <Space className="tw-my-4">
          <span className="tw-text-lg tw-font-semibold tw-text-gray-900">
            Deployment Information
          </span>
        </Space>
        <Form
          onValuesChange={handleFormChange}
          size="large"
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            {CONSTANTS.FORM.deploymentInformation.map((field) => (
              <Col xs={24} sm={field?.width || 12} key={field.name}>
                <FormFields field={field} />
              </Col>
            ))}
          </Row>
        </Form>
      </Card>

      <Card
        className=" tw-border tw-border-solid tw-border-[#D9DBDF] tw-rounded-2xl "
        styles={{
          header: {
            borderBottom: "1px solid #f0f0f0",
            paddingBottom: "0px",
          },
          body: {
            paddingTop: "0px",
          },
        }}
      >
        <div className="tw-space-y-4">
          <Space className="tw-mt-4 tw-flex tw-items-center tw-justify-between">
            <span className="tw-text-lg tw-font-semibold tw-text-gray-900">
              Deployment History
            </span>
            <CommonButton
              icon={<Download className="tw-w-4 tw-h-4 tw-mr-2" />}
              text="Export"
              onClick={() => {
                console.log("Export");
                if (!formData?.websiteId)
                  return message.error("Website not saved");
                exportHandler();
              }}
            />
          </Space>
          <div className="flex justify-between items-center mb-4">
            <div>
              <CommonButton
                text="Trigger Deploy"
                className="tw-mr-2 tw-rounded-lg tw-h-10 tw-px-6"
                loading={apiDeploy.isLoading}
                onClick={() => {
                  console.log("Trigger Deploy");
                  if (!formData?.id) return message.error("Website not saved");
                  triggerDeploy({
                    websiteId: formData?.id,
                    websiteversionId: formData?.websiteversionId,
                  });
                }}
              />
              {/* <CommonButton text="Full Preview" type="default" /> */}
            </div>
          </div>
          <div className="tw-h-[400px] tw-overflow-y-auto tw-pr-2">
            <ScrollPagination
              key={`history-${website?.id}`}
              loadPage={loadHistory}
              idKey="id"
              pageSize={10}
              refreshKey={refreshKey}
              initialPage={1}
              useWindow
              renderItem={(item, index) => {
                return (
                  <List.Item
                    key={index}
                    className=" tw-border tw-rounded-xl tw-border-gray-200 !tw-p-4 tw-my-2"
                  >
                    <div className="tw-flex tw-flex-col tw-w-full tw-space-y-3">
                      <div className="tw-flex tw-justify-between tw-items-center">
                        <div className="tw-flex tw-items-center tw-gap-0">
                          <Tag
                            color="default"
                            className="tw-rounded-xl tw-bg-white"
                          >
                            v{item?.version || 0}
                          </Tag>
                          <Tag
                            className="tw-rounded-xl"
                            color={
                              item?.status === "published" ? "green" : "default"
                            }
                          >
                            {item?.status}
                          </Tag>
                        </div>
                        <div className="tw-text-sm tw-text-gray-500">
                          {item?.createdAt
                            ? new Date(item?.createdAt).toLocaleDateString()
                            : ""}
                          {/* Jul 5 at 7:30 PM */}
                        </div>
                      </div>
                      <div className="tw-flex tw-justify-between tw-items-center">
                        <Text strong>
                          {item?.username ? `${item?.username}@` || "" : ""}
                          {item?.ip || ""}
                        </Text>
                        <div
                          onClick={() => {
                            triggerDeploy(item);
                          }}
                          className="tw-text-sm tw-cursor-pointer tw-text-gray-500 tw-underline tw-text-blue-color"
                        >
                          Promote to production
                        </div>
                      </div>
                    </div>
                  </List.Item>
                );
              }}
              emptyScreen={<Empty description="No deployment history yet" />}
            />
          </div>
        </div>
      </Card>

      <ExportModal
        open={isExport}
        status={status}
        onClose={() => setIsExport(false)}
        onDone={() => setIsExport(false)}
        missing={missing}
      />

      <div className="tw-flex tw-items-center tw-justify-end tw-space-x-2 ">
        <CommonButton onClick={onCancel} type="text" text="Cancel" />
        <CommonButton
          onClick={() => handleSubmit(formData)}
          loading={loading}
          text={"Save Website"}
        />
      </div>
    </div>
  );
};

export default DeploymentTab;
