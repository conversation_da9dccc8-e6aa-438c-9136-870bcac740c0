import React, { useEffect } from "react";
import { Card, Form, Space } from "antd";
import DynamicTabForm from "../../FeaturePage/components/DynamictabForm";
import ExtraModalBody from "../../FeaturePage/components/ExtraModalBody";
import CommonButton from "../../common/CommonButton";

const DynamicVariablesTab = ({
  formData,
  setFormData,
  template,
  globalvariables,
  onCancel,
  handleSubmit,
  loading,
}) => {
  const [form] = Form.useForm();
  // const isServiceAreaD = true;
  // console.log(formData);

  const handleFormChange = (changedValues, allValues) => {
    const extraObj = {};
    // Process simple fields
    if (allValues?.simpleFields) {
      allValues?.simpleFields?.forEach((field) => {
        if (field?.key && field?.value !== undefined) {
          extraObj[field.key] = field.value;
        }
      });
    }

    // Process array groups
    if (allValues?.arrayGroups) {
      allValues?.arrayGroups?.forEach((group) => {
        if (group?.arrayKey && group?.items) {
          const isNestedFormat = group.items[0]?.properties;

          if (isNestedFormat) {
            // New format: array of objects
            extraObj[group.arrayKey] = group.items.map((item) => {
              const singleObject = {};
              item.properties?.forEach((prop) => {
                if (prop && prop.key) {
                  singleObject[prop.key] = prop.value;
                }
              });
              return singleObject;
            });
          } else {
            // Original format: array of {key: value}
            extraObj[group.arrayKey] = group.items.map((item) => ({
              [item.key]: item.value,
            }));
          }
        }
      });
    }
    setFormData((prev) => ({
      ...prev,
      websitevariables: extraObj,
    }));
  };
  //  tab == serviceAreaDKey;
  // const getDefaultFields = () => {
  //   if (isServiceAreaD) {
  //     return [
  //       { key: "breadcrumbName", value: "" },
  //       { key: "sitemapLabel", value: "" },
  //       { key: "state", value: "" },
  //     ];
  //   }
  //   return [
  //     { key: "breadcrumbName", value: "" },
  //     { key: "sitemapLabel", value: "" },
  //   ];
  // };

  useEffect(() => {
    //  if (!open) return;
    const existingExtra = formData?.websitevariables;
    const existingKeys = Object.keys(existingExtra);
    //  parentForm?.getFieldValue([
    //    "sections",
    //    sectionIndex,
    //    "sectionItems",
    //    itemIndex,
    //    "extra",
    //  ]) || {};
    if (Object.keys(existingExtra).length == 0 && globalvariables) {
      const simpleFields = globalvariables?.split(",").map((variable) => ({
        key: variable.trim(),
        value: "",
      }));
      form.setFieldsValue({
        simpleFields,
      });
      return;
    }

    const simpleFields = [];
    const arrayGroups = [];
    console.log("existingExtra", existingExtra);
    // Process existing extra data
    Object.keys(existingExtra).forEach((key) => {
      if (Array.isArray(existingExtra[key])) {
        const firstItem = existingExtra[key][0];
        // Heuristic to check if it's the new format (array of objects) vs old format (array of {key:val})
        const isObjectFormat =
          firstItem &&
          typeof firstItem === "object" &&
          !firstItem.hasOwnProperty("key") &&
          !firstItem.hasOwnProperty("value") &&
          Object.keys(firstItem).length > 0;

        if (isObjectFormat) {
          // New format: array of objects like {name: "...", path: "..."}
          const items = existingExtra[key].map((obj) => {
            const properties = Object.keys(obj).map((k) => ({
              key: k,
              value: obj[k],
            }));
            return { properties };
          });
          arrayGroups.push({ arrayKey: key, items });
        } else {
          // Original format: array of {key: value}
          const items = existingExtra[key].map((item) => ({
            key: Object.keys(item)[0] || "key",
            value: Object.values(item)[0] || "",
          }));
          arrayGroups.push({ arrayKey: key, items });
        }
      } else {
        // This is a single item
        simpleFields.push({ key, value: existingExtra[key] });
      }
    });

    // ✅ now add missing globalvariables (only ones not already present)
    if (globalvariables) {
      const globalKeys = globalvariables?.split(",")?.map((v) => v?.trim());
      globalKeys?.forEach((variable) => {
        if (!existingKeys?.includes(variable)) {
          simpleFields.push({ key: variable, value: "" });
        }
      });
    }

    // Add default fields if not present
    // const defaultFields = getDefaultFields();
    // defaultFields.forEach((defaultField) => {
    //   if (!simpleFields.find((f) => f.key === defaultField.key)) {
    //     simpleFields.unshift(defaultField);
    //   }
    // });

    form.setFieldsValue({
      simpleFields,
      arrayGroups,
    });
  }, [form]);
  return (
    <>
      <Card
        className=" tw-border tw-rounded-2xl tw-border-[#D9DBDF]"
        styles={{
          header: {
            borderBottom: "1px solid #f0f0f0",
            paddingBottom: "0px",
          },
          body: {
            paddingTop: "0px",
          },
        }}
      >
        <Space className="tw-mt-4">
          <span className="tw-text-lg tw-font-semibold tw-text-gray-900">
            Dynamic Variables Details
          </span>
        </Space>
        <ExtraModalBody
          handleFormChange={handleFormChange}
          form={form}
          isServiceAreaD={-1}
          isNested={true}
        />
      </Card>
      <div className="tw-flex tw-items-center tw-justify-end tw-space-x-2 tw-mt-4">
        <CommonButton onClick={onCancel} type="text" text="Cancel" />
        <CommonButton
          onClick={() => handleSubmit(formData)}
          loading={loading}
          text={"Save Website"}
        />
      </div>
    </>
  );
};

export default DynamicVariablesTab;
