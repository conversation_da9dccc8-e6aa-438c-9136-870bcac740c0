import React, { useEffect, useState } from "react";
import { Tabs, message } from "antd";
import { useParams, useNavigate } from "react-router-dom";
import CommonButton from "../common/CommonButton";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import {
  apiGenerator,
  imageObj,
  removeEmptyKeysDeep,
  removeSpacesDeep,
} from "../../util/functions";
import WebsiteTabList from "./components/WebisteTabList";

const { TabPane } = Tabs;

const WebsiteEditor = ({
  websiteData,
  templateData,
  onCancel,
  handleSubmit,
  setRefresh,
  globalvariables,
}) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const api = useHttp();
  // const [website, setWebsite] = useState(null);
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    name: "", // Template name
    // description: "",
    full_template_content: "", // New field: template preview full code
    // templateComponentList: [], // New field: template preview page list
    pageData: [], // Existing field for backward compatibility
    primaryColor: "#38b6ff",
    secondaryColor: "#004e7a",
    backgroundColor: "#ffffff",
    textColor: "#333333",
    borderColor: "#e0e0e0",
    cardBackgroundColor: "#fafafa",
    dividerColor: "#eee",
    websitevariables: {},
    websiteDynamicPageData: {},
    content: {},
    websiteContent: {},
    // content: [], // New field: array of json
  });

  useEffect(() => {
    let obj = {};
    const gObj = {};

    if (globalvariables) {
      globalvariables?.split(",")?.map((v) => {
        gObj[v?.trim()] = "";
      });
    }
    if (websiteData) {
      // obj.websitevariables = websiteData?.websitevariables;
      obj = {
        ...websiteData,
        websitevariables: { ...gObj, ...websiteData?.websitevariables },
      };
    }
    // console.log("main useeffect...");
    setFormData((prev) => ({
      ...prev,
      ...obj,
    }));
    console.log("formData store ");
  }, [globalvariables, websiteData]);

  const handleSave = (submittedFormData) => {
    try {
      setLoading(true);
      // Step 1: Save template data (create or update)
      const websiteApiConfig = websiteData
        ? apiGenerator(
            CONSTANTS.API.websites.update,
            {
              id: websiteData?.websiteId,
              // ...(submittedFormData?.mediaChanged ? { mediaChanged: true } : {}),
            },
            submittedFormData?.mediaChanged
              ? `?mediaChanged=${submittedFormData?.mediaChanged}`
              : null
          )
        : CONSTANTS.API.websites.create;

      const finalPayload = removeSpacesDeep({
        name: formData?.name,
        templateId: formData?.templateId,
        templateVersionId: formData?.templateVersionId,
        primaryColor: formData?.primaryColor,
        secondaryColor: formData?.secondaryColor,
        backgroundColor: formData?.backgroundColor,
        textColor: formData?.textColor,
        borderColor: formData?.borderColor,
        cardBackgroundColor: formData?.cardBackgroundColor,
        dividerColor: formData?.dividerColor,
        websitevariables: removeEmptyKeysDeep(formData?.websitevariables),
        content: formData?.websiteJSON,
        websiteDynamicPageData: formData?.websiteDynamicPageData,
        ip: formData?.ip,
        username: formData?.username,
        path: formData?.path,
        password: formData?.password,
      });
      const websitePayload = websiteData
        ? Object.keys(finalPayload)?.reduce((acc, key) => {
            if (finalPayload[key] !== websiteData[key])
              acc[key] = finalPayload[key];
            return acc;
          }, {})
        : finalPayload;

      api.sendRequest(
        websiteApiConfig,
        async (websiteResponse) => {
          console.log("Website saved successfully:", websiteResponse);

          // Get template ID from response
          const websiteId =
            websiteResponse.data?.website?.id ||
            websiteResponse.websiteVersion?.websiteId ||
            websiteData?.websiteId;
          const websiteVersionData =
            websiteResponse?.websiteVersion ||
            websiteResponse?.data?.websiteVersion;
          const websiteVersionId = websiteVersionData?.id;

          const extraPayload = {
            id: websiteId,
            websiteId: websiteId,
            websiteVersionId: websiteVersionId,
            websiteVersion: websiteVersionData?.version,
            websiteContentId: websiteVersionData?.contentId,
          };

          // Step 2: Handle media upload if there are new files or removals
          if (
            websiteId &&
            (submittedFormData?.newMediaFiles?.length > 0 ||
              submittedFormData?.removeMediaIds?.length > 0)
          ) {
            await handleMediaUpload(
              websiteId,
              websiteVersionId,
              submittedFormData,
              extraPayload
            );
          } else if (!websiteData) {
            await handleMediaUpload(
              websiteId,
              websiteVersionId,
              submittedFormData,
              extraPayload
            );
          } else if (!submittedFormData?.mediaChanged) {
            // Update formData with response
            setFormData((prevData) => ({
              ...prevData,
              // ...(websiteResponse?.data || websiteResponse),
              ...extraPayload,
              // id: templateId,
              // templateId: templateId,
              // templateVersionId: templateVersionId,
              // templateVersion: websiteVersionData?.version,
              // templateContentId: websiteVersionData?.contentId,
            }));
          }

          // Step 3: Execute callback and finish
          // if (extraCall) {
          //   extraCall(websiteResponse);
          // }

          setLoading(false);
          message.success(
            websiteData
              ? "Website updated successfully!"
              : "Website created successfully!"
          );
        },
        websitePayload,
        null, // No automatic success message - we'll handle it manually
        (error) => {
          console.error("Error saving website:", error);
          message.error(error || "Failed to save website. Please try again.");
          setLoading(false);
        }
      );
    } catch (error) {
      console.error("Error creating partial payload, using fallback:", error);
      message.error(error || "Failed to save website.");
      setLoading(false);
    }
  };

  // Handle media upload/removal
  const handleMediaUpload = async (
    websiteId,
    websiteVersionId,
    submittedFormData,
    extraPayload
  ) => {
    try {
      setLoading(true);
      // Prepare FormData for media upload
      const convertFormData = new FormData();
      if (!submittedFormData?.mediaSet) {
        if (!websiteData) {
          navigate(`/websites/${websiteId}`);
        }
        return;
      }

      // Add new media files
      if (
        websiteData?.websiteId &&
        Object.values(submittedFormData?.newMediaFiles)?.length > 0
      ) {
        Object.values(submittedFormData?.newMediaFiles).forEach((mediaFile) => {
          convertFormData.append("media", mediaFile?.file);
        });
      }

      // Add removeMediaIds as comma-separated string
      if (
        websiteData?.websiteId &&
        submittedFormData?.removeMediaIds?.length > 0
      ) {
        convertFormData.append(
          "removeMediaIds",
          submittedFormData.removeMediaIds.join(",")
        );
      }

      if (
        !websiteData?.websiteId &&
        submittedFormData?.mediaSet &&
        Object.values(submittedFormData?.mediaSet)?.length > 0
      ) {
        Object.values(submittedFormData?.newMediaFiles).forEach((mediaFile) => {
          convertFormData.append("media", mediaFile.file);
        });
      }

      // Call media API
      // const mediaApiUrl = `${CONSTANTS.API.templates.addMedia.endpoint}?id=${websiteId}&type=template`;
      // const mediaApiConfig = {
      //   ...CONSTANTS.API.templates.addMedia,
      //   endpoint: mediaApiUrl,
      // };

      api.sendRequest(
        apiGenerator(
          CONSTANTS.API.websites.addMedia,
          {},
          `?id=${websiteVersionId}&type=website`
        ),
        async (mediaResponse) => {
          // console.log("Media uploaded successfully:", mediaResponse);
          if (!websiteData) {
            setFormData((prevData) => ({
              ...prevData,
              mediaChanged: false,
              ...(extraPayload ? extraPayload : {}),
            }));
            // navigate(`/websites/${websiteId}`);
          } else {
            // Fetch updated media list from server after successful upload
            try {
              api.sendRequest(
                apiGenerator(CONSTANTS.API.websites.getById, {
                  id: websiteId,
                }),
                (res) => {
                  console.log("Updated websiteData data:", websiteData);
                  const websitenewData = res?.data;
                  const websiteVersionData = websitenewData?.latestVersion;
                  const updatedMediaSet =
                    websiteVersionData?.mediaSet?.medias || [];

                  setFormData((prevData) => ({
                    ...prevData,
                    websiteId: websitenewData?.id,
                    websiteVersionId: websiteVersionData?.id,
                    websiteVersion: websiteVersionData?.version,
                    websiteContentId: websiteVersionData?.contentId,
                    content: websiteVersionData?.content?.content,
                    websiteJSON: websiteVersionData?.content?.content,
                    templateMediaSet: {
                      ...(websiteVersionData?.templateversion?.mediaSet?.medias
                        ? imageObj(
                            websiteVersionData?.templateversion?.mediaSet
                              ?.medias || {}
                          )
                        : {}),
                    },
                    mediaChanged: false,
                    removeMediaIds: [],
                    newMediaFiles: [],
                    mediaSet: {
                      ...(updatedMediaSet
                        ? imageObj(updatedMediaSet || {})
                        : {}),
                    }, // Update with fresh server data
                    ...(extraPayload ? extraPayload : {}),
                    ...websitenewData,
                  }));

                  // Force re-render of child components to sync state
                  setTimeout(() => {
                    setFormData((prevData) => ({
                      ...prevData,
                      mediaResetTrigger: Date.now(), // Add trigger to force child components to reset
                    }));
                  }, 100);
                  setLoading(false);
                }
              );
            } catch (error) {
              console.error("Error fetching updated media:", error);
              // Fallback to original behavior if refresh fails
              setLoading(false);
              setFormData((prevData) => ({
                ...prevData,
                mediaChanged: false,
                removeMediaIds: [],
                newMediaFiles: [],
                ...(extraPayload ? extraPayload : {}),
              }));

              setTimeout(() => {
                setFormData((prevData) => ({
                  ...prevData,
                  mediaResetTrigger: Date.now(),
                }));
              }, 100);
            }
          }
          // message.success("Media files processed successfully!");
        },
        convertFormData,
        null, // No automatic success message
        (error) => {
          setLoading(false);
          console.error("Error uploading media:", error);
          message.error(
            error ||
              "Failed to process media files. Website saved but media upload failed."
          );
        }
      );
    } catch (error) {
      setLoading(false);
      console.error("Error in handleMediaUpload:", error);
      message.error(error || "Failed to process media files.");
    }
  };

  return (
    <div className=" tw-flex tw-flex-col tw-space-y-6  tw-px-4 tw-pb-6 ">
      <WebsiteTabList
        websiteData={websiteData}
        // setWebsite={setWebsite}
        templateData={templateData}
        formData={formData}
        setFormData={setFormData}
        loading={loading}
        setLoading={setLoading}
        onCancel={onCancel}
        handleSubmit={handleSave}
        globalvariables={globalvariables}
        // handleSave={handleSave}
      />
    </div>
  );
};

export default WebsiteEditor;
