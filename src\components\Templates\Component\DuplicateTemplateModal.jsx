import React from "react";
import { Modal, Form } from "antd";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import FormFields from "../../common/FormFields";
import CommonButton from "../../common/CommonButton";

const DuplicateTemplateModal = ({ visible, onCancel, template, onSuccess }) => {
  const [form] = Form.useForm();
  const api = useHttp();

  const handleFinish = (values) => {
    const payload = {
      ...values,
      id: template?.id,
    };
    delete payload.name;
    api.sendRequest(
      CONSTANTS.API.templates.copy,
      (res) => {
        onSuccess({
          ...res?.data,
          templateversions: res?.data?.versions,
          // latestVersion: res?.data?.versions?.[0],
        });
        form.resetFields();
        onCancel();
      },
      payload
    );
  };

  const duplicateTemplateForm = CONSTANTS.FORM.duplicateTemplate?.map(
    (field) => {
      if (field.name === "versionId") {
        return {
          ...field,
          options: template?.templateversions?.map((v) => ({
            label: `v${v?.version}`,
            value: v?.id,
          })),
        };
      }
      return field;
    }
  );

  return (
    <Modal
      title="Duplicate Template"
      visible={visible}
      onCancel={onCancel}
      footer={null}
      destroyOnClose
    >
      <Form
        initialValues={{
          versionId: template?.templateversions?.[0]?.id,
          name: template?.name || "",
        }}
        form={form}
        layout="vertical"
        size="large"
        onFinish={handleFinish}
      >
        {duplicateTemplateForm?.map((field) => (
          <FormFields key={field.name} field={field} />
        ))}
        <div className="tw-flex tw-justify-end tw-gap-2">
          <CommonButton onClick={onCancel} text="Cancel" type="default" />
          <CommonButton
            htmlType="submit"
            loading={api.isLoading}
            text="Create"
          />
        </div>
      </Form>
    </Modal>
  );
};

export default DuplicateTemplateModal;
