import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  Card,
  Tag,
  Avatar,
  Button,
  Space,
  Typography,
  Tooltip,
  Empty,
  Spin,
} from "antd";
import {
  Activity,
  Eye,
  Plus,
  Edit,
  Trash2,
  Copy,
  Download,
  Rocket,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import SearchBar from "../common/SearchBar";
import ActivityLogModal from "./ActivityLogModal";
import useDebounce from "../../hooks/useDebounce";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
const { Title, Text } = Typography;

// Get action icon
const getActionIcon = (action) => {
  const iconMap = {
    add: <Plus className="tw-w-3 tw-h-3" />,
    update: <Edit className="tw-w-3 tw-h-3" />,
    delete: <Trash2 className="tw-w-3 tw-h-3" />,
    copy: <Copy className="tw-w-3 tw-h-3" />,
    export: <Download className="tw-w-3 tw-h-3" />,
    deploy: <Rocket className="tw-w-3 tw-h-3" />,
  };
  return iconMap[action] || <Edit className="tw-w-3 tw-h-3" />;
};

// Get action color
const getActionColor = (action) => {
  const colorMap = {
    add: "green",
    update: "blue",
    delete: "red",
    copy: "orange",
    export: "purple",
    deploy: "cyan",
  };
  return colorMap[action] || "default";
};

const Activitytable = ({
  activityLogs,
  setActivityLogs,

  isSearch = true,
  isPagination = true,
}) => {
  const api = useHttp();
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const [selectedLog, setSelectedLog] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 8,
    total: 0,
  });
  const [filters, setFilters] = useState({});
  const [sorter, setSorter] = useState({});

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Format date and time - moved outside to avoid dependency issues
  const formatDateTime = (dateString) => {
    if (!dateString) return "N/A";
    // const date = new Date(dateString);
    // const day = date.getDate().toString().padStart(2, "0");
    // const month = (date.getMonth() + 1).toString().padStart(2, "0");
    // const year = date.getFullYear();
    // const hours = date.getHours();
    // const minutes = date.getMinutes().toString().padStart(2, "0");
    // const ampm = hours >= 12 ? "PM" : "AM";
    // const displayHours = hours % 12 || 12;

    // return `${day}/${month}/${year}, ${displayHours}:${minutes} ${ampm}`;
    // give optimize
    return new Date(dateString).toLocaleString();
  };

  // Load activity logs with API pagination
  const loadActivityLogs = useCallback(
    async (params = {}) => {
      setLoading(true);
      try {
        // Build query parameters
        const queryParams = {
          page: params.page || pagination.current,
          limit: params.limit || pagination.pageSize,
          search: debouncedSearchTerm || params.search,
          entityType: params.entityType || filters.entityType?.[0],
          ...params,
        };

        // Remove undefined values
        const cleanParams = Object.fromEntries(
          Object.entries(queryParams).filter(
            ([_, value]) =>
              value !== undefined && value !== null && value !== ""
          )
        );

        await api.sendRequest(
          {
            ...CONSTANTS.API.userActivityLogs.get,
            endpoint: `${
              CONSTANTS.API.userActivityLogs.get.endpoint
            }?${new URLSearchParams(cleanParams).toString()}`,
          },
          (res) => {
            const formattedLogs =
              res?.data?.rows?.map((log) => ({
                ...log,
                formattedDate: formatDateTime(log.createdAt),
                user: {
                  name: log.user?.name || "Unknown User",
                  email: log.user?.email || "No email",
                },
              })) || [];

            setActivityLogs({
              rows: formattedLogs,
              count: res?.data?.count,
            });
            setPagination((prev) => ({
              ...prev,
              total: res?.data?.count || 0,
              current: params.page || prev.current,
              pageSize: params.limit || prev.pageSize,
            }));
          }
        );
      } catch (error) {
        console.error("Error loading activity logs:", error);
      } finally {
        setLoading(false);
      }
    },
    [] // Only depend on api, not on pagination/filters/search
  );

  // No client-side filtering needed - handled by API

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    // Reset to first page when searching
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // Handle search term changes
  useEffect(() => {
    if (debouncedSearchTerm !== undefined && debouncedSearchTerm !== "") {
      loadActivityLogs({ search: debouncedSearchTerm, page: 1 });
    }
  }, [debouncedSearchTerm]); // Remove loadActivityLogs from dependencies

  // Handle modal
  const handleViewMessage = (log) => {
    setSelectedLog(log);
    setModalVisible(true);
  };

  const activityLogColumns = [
    {
      title: "User",
      dataIndex: "user",
      key: "user",
      width: 200,
      render: (user) => (
        <div className="tw-flex tw-items-center tw-gap-3">
          <Avatar size="default" className="tw-bg-blue-color tw-text-white">
            {user?.name?.charAt(0)?.toUpperCase() || "U"}
          </Avatar>
          <div>
            <div className="tw-font-medium tw-text-gray-900">
              {user?.name || "Unknown User"}
            </div>
            <div className="tw-text-sm tw-text-gray-500">
              {user?.email || "No email"}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Entity Type",
      dataIndex: "entityType",
      key: "entityType",
      width: 120,
      filters: [
        { text: "Page", value: "Page" },
        { text: "Component", value: "Component" },
        { text: "Template", value: "Template" },
        { text: "Category", value: "Category" },
      ],
      onFilter: (value, record) => record.entityType === value,
      render: (entityType) => <Tag color="blue">{entityType}</Tag>,
    },
    {
      title: "Date & Time",
      dataIndex: "formattedDate",
      key: "formattedDate",
      width: 150,
      render: (date) => <Text className="tw-text-sm">{date}</Text>,
    },
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      width: 120,
      render: (action) => (
        <Tag
          color={getActionColor(action)}
          icon={getActionIcon(action)}
          className="tw-flex tw-items-center tw-gap-1 tw-rounded-lg tw-w-20"
        >
          {action?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: "Message",
      key: "actions",
      width: 50,
      align: "center",
      render: (_, record) => (
        <Tooltip title="View Details">
          <Button
            type="text"
            size="small"
            icon={<Eye className="tw-w-4 tw-h-4" />}
            onClick={() => handleViewMessage(record)}
            className="tw-text-gray-500 hover:tw-text-blue-500"
          />
        </Tooltip>
      ),
    },
  ];

  const handleCloseModal = () => {
    setModalVisible(false);
    setSelectedLog(null);
  };

  // Handle table changes (filtering, sorting, pagination)
  const handleTableChange = (paginationInfo, filters, sorter, extra) => {
    console.log("Table change:", { paginationInfo, filters, sorter, extra });

    // Update local state
    setFilters(filters);
    setSorter(sorter);

    // Load data with new parameters
    const params = {
      page: paginationInfo.current,
      limit: paginationInfo.pageSize,
      entityType: filters.entityType?.[0],
      action: filters.action?.[0],
    };

    loadActivityLogs(params);
  };

  // Load data on component mount
  useEffect(() => {
    loadActivityLogs();
  }, []); // Empty dependency array to run only once on mount
  return (
    <>
      {isSearch && (
        <SearchBar
          type="page"
          placeholder="Search by user, email, or message..."
          handleSearch={setSearchTerm}
        />
      )}
      <div className="custom-table">
        <Table
          columns={activityLogColumns}
          dataSource={activityLogs?.rows}
          loading={loading}
          rowKey="id"
          onChange={handleTableChange}
          pagination={
            isPagination && {
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              //   showSizeChanger: true,
              //   showQuickJumper: true,
              //   showTotal: (total, range) =>
              //     `${range[0]}-${range[1]} of ${total} activity logs`,
            }
          }
          locale={{
            emptyText: (
              <Empty
                description="No activity logs found"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ),
          }}
          scroll={{ x: 800 }}
          showSorterTooltip={{ target: "sorter-icon" }}
        />
      </div>

      {/* Modal */}
      <ActivityLogModal
        visible={modalVisible}
        onClose={handleCloseModal}
        activityLog={selectedLog}
      />
    </>
  );
};

export default Activitytable;
